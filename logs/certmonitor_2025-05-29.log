2025-05-29 20:52:18 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 20:53:59 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 20:53:59 | INFO     | __main__:test_logging_setup:61 - 这是一条测试日志消息
2025-05-29 20:57:15 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 20:57:15 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 10.211.55.21:53306...
2025-05-29 20:57:15 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 20:57:15 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 20:57:15 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 20:57:15 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 20:57:15 | INFO     | app.core.manager:_check_first_run:87 - 检测到首次运行，已有域名但没有子域名
2025-05-29 20:57:15 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 20:57:15 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 20:57:15 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:68 - 更新CT Log监控域名: 1 个
2025-05-29 20:57:15 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt, project/联想.txt
2025-05-29 20:57:15 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-29 20:57:15 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-29 20:57:15 | INFO     | app.utils.config:get_domain_files:225 - 找到 1 个域名文件:
2025-05-29 20:57:15 | INFO     | app.utils.config:get_domain_files:227 -   - project/联想.txt
2025-05-29 20:57:15 | INFO     | app.utils.config:load_domains_from_files:389 - 准备从 1 个域名文件中加载域名
2025-05-29 20:57:15 | INFO     | app.utils.config:load_domains_from_files:405 - 已从 project/联想.txt 加载 1 个域名
2025-05-29 20:57:15 | INFO     | app.utils.config:load_domains_from_files:415 - 总共加载了 1 个唯一域名
2025-05-29 20:57:15 | INFO     | app.core.manager:start_monitoring:215 - 检测到首次运行，将进行初始化扫描
2025-05-29 20:57:15 | INFO     | app.core.manager:start_monitoring:220 - 首次运行，暂时禁用通知
2025-05-29 20:57:15 | INFO     | app.notifiers.notifier:start:81 - 通知功能已禁用
2025-05-29 20:57:15 | INFO     | app.core.manager:start_monitoring:231 - 开始对 1 个域名进行初始扫描...
2025-05-29 20:57:15 | INFO     | app.core.manager:start_monitoring:240 - 正在使用crt.sh查询 lenovo.com 的子域名...
2025-05-29 20:57:15 | INFO     | app.core.crtsh_monitor:check_domain:229 - 正在查询crt.sh获取 lenovo.com 的子域名...
2025-05-29 21:14:25 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:15:29 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:16:43 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:16:43 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 10.211.55.21:53306...
2025-05-29 21:16:43 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:16:43 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:16:43 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:16:43 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 21:16:43 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 21:16:43 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 21:16:43 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:81 - 更新CT Log监控域名: 1 个
2025-05-29 21:16:43 | WARNING  | app.core.manager:add_domain:380 - 域名 lenovo.com 已在监控列表中
2025-05-29 21:17:38 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:18:33 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
