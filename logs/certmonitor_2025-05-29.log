2025-05-29 20:52:18 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 20:53:59 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 20:53:59 | INFO     | __main__:test_logging_setup:61 - 这是一条测试日志消息
2025-05-29 20:57:15 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 20:57:15 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 20:57:15 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 20:57:15 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 20:57:15 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 20:57:15 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 20:57:15 | INFO     | app.core.manager:_check_first_run:87 - 检测到首次运行，已有域名但没有子域名
2025-05-29 20:57:15 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 20:57:15 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 20:57:15 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:68 - 更新CT Log监控域名: 1 个
2025-05-29 20:57:15 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt, project/联想.txt
2025-05-29 20:57:15 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-29 20:57:15 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-29 20:57:15 | INFO     | app.utils.config:get_domain_files:225 - 找到 1 个域名文件:
2025-05-29 20:57:15 | INFO     | app.utils.config:get_domain_files:227 -   - project/联想.txt
2025-05-29 20:57:15 | INFO     | app.utils.config:load_domains_from_files:389 - 准备从 1 个域名文件中加载域名
2025-05-29 20:57:15 | INFO     | app.utils.config:load_domains_from_files:405 - 已从 project/联想.txt 加载 1 个域名
2025-05-29 20:57:15 | INFO     | app.utils.config:load_domains_from_files:415 - 总共加载了 1 个唯一域名
2025-05-29 20:57:15 | INFO     | app.core.manager:start_monitoring:215 - 检测到首次运行，将进行初始化扫描
2025-05-29 20:57:15 | INFO     | app.core.manager:start_monitoring:220 - 首次运行，暂时禁用通知
2025-05-29 20:57:15 | INFO     | app.notifiers.notifier:start:81 - 通知功能已禁用
2025-05-29 20:57:15 | INFO     | app.core.manager:start_monitoring:231 - 开始对 1 个域名进行初始扫描...
2025-05-29 20:57:15 | INFO     | app.core.manager:start_monitoring:240 - 正在使用crt.sh查询 lenovo.com 的子域名...
2025-05-29 20:57:15 | INFO     | app.core.crtsh_monitor:check_domain:229 - 正在查询crt.sh获取 lenovo.com 的子域名...
2025-05-29 21:14:25 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:15:29 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:16:43 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:16:43 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 21:16:43 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:16:43 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:16:43 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:16:43 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 21:16:43 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 21:16:43 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 21:16:43 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:81 - 更新CT Log监控域名: 1 个
2025-05-29 21:16:43 | WARNING  | app.core.manager:add_domain:380 - 域名 lenovo.com 已在监控列表中
2025-05-29 21:17:38 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:18:33 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:27:35 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:27:35 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 21:27:35 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:27:35 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:27:35 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:27:35 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 21:27:35 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 21:27:35 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 21:27:35 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:87 - 更新CT Log监控域名: 1 个
2025-05-29 21:27:35 | INFO     | app.core.manager:_start_alive_notification:332 - 存活通知已启动，间隔: 0.008333333333333333小时
2025-05-29 21:28:20 | INFO     | app.core.manager:_stop_alive_notification:339 - 存活通知已停止
2025-05-29 21:43:58 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:43:58 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 21:43:59 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:43:59 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:43:59 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:43:59 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 21:43:59 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 21:43:59 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 21:43:59 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:87 - 更新CT Log监控域名: 1 个
2025-05-29 21:43:59 | ERROR    | app.core.manager:_send_alive_notification:385 - 发送存活通知失败: 'Notifier' object has no attribute 'send_notification'
2025-05-29 21:44:57 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:44:57 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 21:44:57 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:44:57 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:44:57 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:44:57 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 21:44:57 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 21:44:57 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 21:44:57 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:87 - 更新CT Log监控域名: 1 个
2025-05-29 21:44:59 | INFO     | app.notifiers.notifier:_send_request:225 - 微信通知发送成功
2025-05-29 21:44:59 | INFO     | app.core.manager:_send_alive_notification:376 - 已发送存活确认通知
2025-05-29 21:48:55 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:48:55 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 21:48:55 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:48:56 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:48:56 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:48:56 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 21:48:56 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 21:48:56 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 21:48:56 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:87 - 更新CT Log监控域名: 1 个
2025-05-29 21:48:56 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt, project/联想.txt
2025-05-29 21:48:56 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-29 21:48:56 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-29 21:48:56 | INFO     | app.utils.config:get_domain_files:225 - 找到 1 个域名文件:
2025-05-29 21:48:56 | INFO     | app.utils.config:get_domain_files:227 -   - project/联想.txt
2025-05-29 21:48:56 | INFO     | app.utils.config:load_domains_from_files:389 - 准备从 1 个域名文件中加载域名
2025-05-29 21:48:56 | INFO     | app.utils.config:load_domains_from_files:405 - 已从 project/联想.txt 加载 1 个域名
2025-05-29 21:48:56 | INFO     | app.utils.config:load_domains_from_files:415 - 总共加载了 1 个唯一域名
2025-05-29 21:48:56 | INFO     | app.core.manager:_start_heartbeat:249 - 心跳监控已启动，间隔: 60秒
2025-05-29 21:48:56 | INFO     | app.core.manager:_start_alive_notification:333 - 存活通知已启动，间隔: 6.0小时
2025-05-29 21:48:56 | INFO     | app.core.manager:start_monitoring:398 - 发送启动确认通知
2025-05-29 21:49:16 | ERROR    | app.notifiers.notifier:_send_request:239 - 微信通知发送出错: HTTPSConnectionPool(host='qyapi.weixin.qq.com', port=443): Max retries exceeded with url: /cgi-bin/webhook/send?key=e4fd3203-0828-4545-9632-5f91297d51ff (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fa484763620>, 'Connection to qyapi.weixin.qq.com timed out. (connect timeout=10)'))
2025-05-29 21:49:16 | INFO     | app.notifiers.notifier:_send_request:244 - 等待 20 秒后重试...
2025-05-29 21:50:01 | ERROR    | app.notifiers.notifier:_send_request:239 - 微信通知发送出错: HTTPSConnectionPool(host='qyapi.weixin.qq.com', port=443): Max retries exceeded with url: /cgi-bin/webhook/send?key=e4fd3203-0828-4545-9632-5f91297d51ff (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fa484499810>, 'Connection to qyapi.weixin.qq.com timed out. (connect timeout=10)'))
2025-05-29 21:50:01 | INFO     | app.notifiers.notifier:_send_request:244 - 等待 40 秒后重试...
2025-05-29 21:51:08 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:51:08 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 21:51:08 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:51:09 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:51:09 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:51:28 | INFO     | app.notifiers.notifier:_send_request:225 - 微信通知发送成功
2025-05-29 21:52:27 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 21:52:27 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 21:52:27 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:52:27 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:52:27 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:52:27 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 21:52:27 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 21:52:27 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 21:52:27 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:87 - 更新CT Log监控域名: 1 个
2025-05-29 21:52:27 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt, project/联想.txt
2025-05-29 21:52:27 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-29 21:52:27 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-29 21:52:27 | INFO     | app.utils.config:get_domain_files:225 - 找到 1 个域名文件:
2025-05-29 21:52:27 | INFO     | app.utils.config:get_domain_files:227 -   - project/联想.txt
2025-05-29 21:52:27 | INFO     | app.utils.config:load_domains_from_files:389 - 准备从 1 个域名文件中加载域名
2025-05-29 21:52:27 | INFO     | app.utils.config:load_domains_from_files:405 - 已从 project/联想.txt 加载 1 个域名
2025-05-29 21:52:27 | INFO     | app.utils.config:load_domains_from_files:415 - 总共加载了 1 个唯一域名
2025-05-29 21:52:27 | INFO     | app.core.manager:_start_heartbeat:249 - 心跳监控已启动，间隔: 60秒
2025-05-29 21:52:27 | INFO     | app.core.manager:_start_alive_notification:333 - 存活通知已启动，间隔: 6.0小时
2025-05-29 21:52:27 | INFO     | app.core.manager:start_monitoring:398 - 发送启动确认通知
2025-05-29 21:52:28 | INFO     | app.notifiers.notifier:_send_request:225 - 微信通知发送成功
2025-05-29 21:52:28 | INFO     | app.core.manager:_send_alive_notification:376 - 已发送存活确认通知
2025-05-29 21:52:28 | INFO     | app.notifiers.notifier:start:93 - 通知器已启动
2025-05-29 21:52:28 | INFO     | app.core.crtsh_monitor:start:98 - crt.sh监控已启动，间隔: 24.0小时
2025-05-29 21:52:28 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:170 - 开始持续监控CT日志: Let's Encrypt Oak 2025h1
2025-05-29 21:52:28 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:170 - 开始持续监控CT日志: Let's Encrypt Oak 2025h2
2025-05-29 21:52:28 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:170 - 开始持续监控CT日志: Cloudflare Nimbus 2025
2025-05-29 21:52:28 | INFO     | app.core.crtsh_monitor:check_all_domains:298 - 开始查询域名: lenovo.com
2025-05-29 21:52:28 | INFO     | app.core.crtsh_monitor:check_domain:229 - 正在查询crt.sh获取 lenovo.com 的子域名...
2025-05-29 21:52:28 | INFO     | app.core.ctlog_monitor:start:138 - CT Log监控已启动，监控 3 个日志
2025-05-29 21:52:28 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt, project/联想.txt
2025-05-29 21:52:28 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-29 21:52:28 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-29 21:52:28 | INFO     | app.utils.config:get_domain_files:225 - 找到 1 个域名文件:
2025-05-29 21:52:28 | INFO     | app.utils.config:get_domain_files:227 -   - project/联想.txt
2025-05-29 21:52:28 | INFO     | app.utils.config:start_domain_files_monitor:380 - 域名文件监控已启动，检查间隔: 60分钟
2025-05-29 21:52:28 | INFO     | app.core.manager:start_monitoring:457 - 域名文件动态加载已启动
2025-05-29 21:52:28 | INFO     | app.core.manager:start_monitoring:459 - 所有监控器已启动
2025-05-29 21:52:34 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:179 - Let's Encrypt Oak 2025h2: 从位置 588408517 开始监控
2025-05-29 21:52:37 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:179 - Cloudflare Nimbus 2025: 从位置 1625747395 开始监控
2025-05-29 21:52:37 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:179 - Let's Encrypt Oak 2025h1: 从位置 986651243 开始监控
2025-05-29 21:52:38 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:234 - Let's Encrypt Oak 2025h2: 处理 50 个新条目，从 588408517 开始 (总大小: 588408840)
2025-05-29 21:52:41 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:234 - Cloudflare Nimbus 2025: 处理 10 个新条目，从 1625747395 开始 (总大小: 1625747405)
2025-05-29 21:52:42 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [CLOUDFLARE] 接收到证书 #1625747395: snigllc.com, snigllc.com
2025-05-29 21:52:43 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:234 - Let's Encrypt Oak 2025h1: 处理 13 个新条目，从 986651243 开始 (总大小: 986651256)
2025-05-29 21:52:44 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [CLOUDFLARE] 接收到证书 #1625747396: aupouletdor.be, aupouletdor.be
2025-05-29 21:52:45 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #588408517: 证书数据不完整 (需要2865896字节，只有1107字节)
2025-05-29 21:52:48 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [CLOUDFLARE] 接收到证书 #1625747397: amtsbergschule.de, amtsbergschule.de, www.amtsbergschule.de
2025-05-29 21:53:01 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [CLOUDFLARE] 接收到证书 #1625747398: direwolf-3faf550070-c6ed29252161.pear-virginia.herokuapp.com, direwolf-3faf550070-c6ed29252161.pear-virginia.herokuapp.com
2025-05-29 21:53:03 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [CLOUDFLARE] 接收到证书 #1625747399: www.platemates.co.uk, *.co.uk.craigfairgrieve.com, cpanel.platemates.co.uk (共7个)
2025-05-29 21:53:11 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986651243: mind-master.pages.dev, mind-master.pages.dev, *.mind-master.pages.dev
2025-05-29 21:53:15 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:287 - Let's Encrypt Oak 2025h2: 获取条目 #588408518 网络错误: HTTPSConnectionPool(host='oak.ct.letsencrypt.org', port=443): Max retries exceeded with url: /2025h2/ct/v1/get-entries?start=588408518&end=588408518 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f60937d9090>, 'Connection to oak.ct.letsencrypt.org timed out. (connect timeout=30)'))
2025-05-29 21:53:21 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #588408519: 证书数据不完整 (需要2865896字节，只有1076字节)
2025-05-29 21:53:43 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:287 - Let's Encrypt Oak 2025h1: 获取条目 #986651244 网络错误: HTTPSConnectionPool(host='oak.ct.letsencrypt.org', port=443): Max retries exceeded with url: /2025h1/ct/v1/get-entries?start=986651244&end=986651244 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f60937d9590>, 'Connection to oak.ct.letsencrypt.org timed out. (connect timeout=30)'))
2025-05-29 21:53:50 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986651245: johannadenisevl9vy.pages.dev, johannadenisevl9vy.pages.dev, *.johannadenisevl9vy.pages.dev
2025-05-29 21:53:51 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:287 - Let's Encrypt Oak 2025h2: 获取条目 #588408520 网络错误: HTTPSConnectionPool(host='oak.ct.letsencrypt.org', port=443): Max retries exceeded with url: /2025h2/ct/v1/get-entries?start=588408520&end=588408520 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f60940b7d90>, 'Connection to oak.ct.letsencrypt.org timed out. (connect timeout=30)'))
2025-05-29 21:54:08 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:287 - Cloudflare Nimbus 2025: 获取条目 #1625747400 网络错误: HTTPSConnectionPool(host='ct.cloudflare.com', port=443): Max retries exceeded with url: /logs/nimbus2025/ct/v1/get-entries?start=1625747400&end=1625747400 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f60937d8410>: Failed to establish a new connection: [Errno 101] Network is unreachable'))
2025-05-29 21:54:09 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [CLOUDFLARE] 接收到证书 #1625747401: app.fahrschulehagen.de, app.fahrschulehagen.de
2025-05-29 21:54:12 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [CLOUDFLARE] 接收到证书 #1625747402: vdmol23c8pglvetbjt4in25dfgjkbsq.direct.quickconnect.to, *.vdmol23c8pglvetbjt4in25dfgjkbsq.direct.quickconnect.to, vdmol23c8pglvetbjt4in25dfgjkbsq.direct.quickconnect.to
2025-05-29 21:54:13 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1625747403: 证书数据不完整 (需要9471864字节，只有643字节)
2025-05-29 21:54:17 | DEBUG    | app.core.ctlog_monitor:_process_entry:337 - 条目 #1625747404: 证书长度异常 (16670732 字节)
2025-05-29 21:54:17 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:300 - Cloudflare Nimbus 2025: 处理了 9 个条目，匹配 0 个，跳过错误 1 个
2025-05-29 21:54:20 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:287 - Let's Encrypt Oak 2025h1: 获取条目 #986651246 网络错误: HTTPSConnectionPool(host='oak.ct.letsencrypt.org', port=443): Max retries exceeded with url: /2025h1/ct/v1/get-entries?start=986651246&end=986651246 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f60940b7ed0>, 'Connection to oak.ct.letsencrypt.org timed out. (connect timeout=30)'))
2025-05-29 21:54:21 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986651247: nayapark.com, nayapark.com, *.nayapark.com
2025-05-29 21:54:22 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:287 - Let's Encrypt Oak 2025h2: 获取条目 #588408521 网络错误: HTTPSConnectionPool(host='oak.ct.letsencrypt.org', port=443): Max retries exceeded with url: /2025h2/ct/v1/get-entries?start=588408521&end=588408521 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f60937d8190>, 'Connection to oak.ct.letsencrypt.org timed out. (connect timeout=30)'))
2025-05-29 21:54:25 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986651248: pay.whimsyandwonders.com, pay.whimsyandwonders.com, www.pay.whimsyandwonders.com
2025-05-29 21:54:29 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986651249: seksoyuncak.shop, seksoyuncak.shop
2025-05-29 21:54:32 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #588408522: rdweb.wkrgbv.com, rdweb.wkrgbv.com
2025-05-29 21:54:34 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #588408523: 证书数据不完整 (需要7199440字节，只有831字节)
2025-05-29 21:54:36 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986651250: istn.me, istn.me, *.istn.me
2025-05-29 21:54:38 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986651251: clidedelaney.co.uk, clidedelaney.co.uk
2025-05-29 21:54:43 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #986651252: 证书数据不完整 (需要2614967字节，只有1033字节)
2025-05-29 21:58:51 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: INFO, 文件模式: 按日期分割
2025-05-29 21:58:51 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 21:58:51 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:58:52 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:58:52 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:58:52 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 0 个CT日志配置
2025-05-29 21:58:52 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 21:58:52 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 21:59:51 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: INFO, 文件模式: 按日期分割
2025-05-29 21:59:51 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 21:59:52 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 21:59:52 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 21:59:52 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 21:59:52 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 0 个CT日志配置
2025-05-29 21:59:52 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 21:59:52 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:01:06 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: INFO, 文件模式: 按日期分割
2025-05-29 22:01:06 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 22:01:06 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 22:01:07 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 22:01:07 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 22:01:07 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:01:07 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:01:07 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:04:07 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: INFO, 文件模式: 按日期分割
2025-05-29 22:04:07 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 22:04:07 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 22:04:08 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 22:04:08 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 22:04:08 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:04:08 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:04:08 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:04:08 | INFO     | app.utils.config:get_domain_files:225 - 找到 1 个域名文件:
2025-05-29 22:04:08 | INFO     | app.utils.config:get_domain_files:227 -   - app/config/custom_domains.txt
2025-05-29 22:04:08 | INFO     | app.utils.config:load_domains_from_files:389 - 准备从 1 个域名文件中加载域名
2025-05-29 22:04:08 | INFO     | app.utils.config:load_domains_from_files:405 - 已从 app/config/custom_domains.txt 加载 14 个域名
2025-05-29 22:04:08 | INFO     | app.utils.config:load_domains_from_files:415 - 总共加载了 14 个唯一域名
2025-05-29 22:04:08 | INFO     | app.core.manager:remove_domain:591 - 已从监控列表中移除 lenovo.com (ID: 11, 子域名数量: 1018)
2025-05-29 22:04:08 | INFO     | app.core.manager:add_domain:490 - 已添加 mit.edu 到监控列表
2025-05-29 22:04:08 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:04:08 | INFO     | app.core.manager:add_domain:506 - 正在使用crt.sh查询 mit.edu 的子域名...
2025-05-29 22:04:08 | INFO     | app.core.crtsh_monitor:check_domain:229 - 正在查询crt.sh获取 mit.edu 的子域名...
2025-05-29 22:04:26 | INFO     | app.core.crtsh_monitor:query_crt_sh:126 - 正在查询crt.sh: mit.edu (尝试 1/5)
2025-05-29 22:06:09 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: INFO, 文件模式: 按日期分割
2025-05-29 22:06:09 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 22:06:09 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 22:06:09 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 22:06:09 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 22:06:09 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:06:09 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:06:09 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:06:09 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:07:13 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: INFO, 文件模式: 按日期分割
2025-05-29 22:07:13 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 22:07:13 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 22:07:14 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 22:07:14 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 22:07:14 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:07:14 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:07:14 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:07:14 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:07:14 | INFO     | app.utils.config:get_domain_files:225 - 找到 1 个域名文件:
2025-05-29 22:07:14 | INFO     | app.utils.config:get_domain_files:227 -   - project/联想.txt
2025-05-29 22:07:14 | INFO     | app.utils.config:load_domains_from_files:389 - 准备从 1 个域名文件中加载域名
2025-05-29 22:07:14 | INFO     | app.utils.config:load_domains_from_files:405 - 已从 project/联想.txt 加载 1 个域名
2025-05-29 22:07:14 | INFO     | app.utils.config:load_domains_from_files:415 - 总共加载了 1 个唯一域名
2025-05-29 22:07:14 | INFO     | app.core.manager:remove_domain:591 - 已从监控列表中移除 mit.edu (ID: 12, 子域名数量: 0)
2025-05-29 22:07:14 | INFO     | app.core.manager:add_domain:490 - 已添加 lenovo.com 到监控列表
2025-05-29 22:07:14 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:07:14 | INFO     | app.core.manager:add_domain:500 - 首次运行，暂时禁用通知
2025-05-29 22:07:14 | INFO     | app.core.manager:add_domain:506 - 正在使用crt.sh查询 lenovo.com 的子域名...
2025-05-29 22:07:14 | INFO     | app.core.crtsh_monitor:check_domain:229 - 正在查询crt.sh获取 lenovo.com 的子域名...
2025-05-29 22:07:29 | INFO     | app.core.crtsh_monitor:query_crt_sh:126 - 正在查询crt.sh: lenovo.com (尝试 1/5)
2025-05-29 22:08:05 | INFO     | app.core.manager:add_domain:516 - 恢复通知设置
2025-05-29 22:09:35 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: INFO, 文件模式: 按日期分割
2025-05-29 22:09:35 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 22:09:35 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 22:09:35 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 22:09:35 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 22:09:35 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:09:35 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:09:35 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:09:35 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:11:25 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: INFO, 文件模式: 按日期分割
2025-05-29 22:11:25 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 22:11:25 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 22:11:25 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 22:11:25 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 22:11:25 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:11:25 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:11:25 | INFO     | app.utils.config:register_domain_files_callback:260 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:11:25 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:12:08 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 22:12:08 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 22:12:08 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 22:12:08 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 22:12:08 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 22:12:08 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:12:08 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:12:08 | INFO     | app.utils.config:register_domain_files_callback:260 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:12:08 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:12:08 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:87 - 更新CT Log监控域名: 1 个
2025-05-29 22:12:43 | INFO     | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: INFO, 文件模式: 按日期分割
2025-05-29 22:12:43 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 22:12:43 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 22:12:43 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 22:12:43 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 22:12:43 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:12:43 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:12:43 | INFO     | app.utils.config:register_domain_files_callback:260 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:12:43 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:16:44 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:16:44 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:16:44 | INFO     | app.utils.config:register_domain_files_callback:260 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:16:44 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:18:41 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:18:41 | INFO     | app.utils.config:register_domain_files_callback:260 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:19:23 | DEBUG    | app.utils.logger:setup_logging:85 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-29 22:19:23 | DEBUG    | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 ************:53306...
2025-05-29 22:19:23 | DEBUG    | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-29 22:19:23 | DEBUG    | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-29 22:19:23 | DEBUG    | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-29 22:19:23 | DEBUG    | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-29 22:19:23 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:19:23 | INFO     | app.utils.config:register_domain_files_callback:260 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:19:23 | DEBUG    | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-29 22:19:23 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:87 - 更新CT Log监控域名: 1 个
2025-05-29 22:21:47 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:21:47 | INFO     | app.utils.config:register_domain_files_callback:260 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:21:52 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:21:52 | INFO     | app.utils.config:register_domain_files_callback:260 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:22:02 | INFO     | app.core.manager:_check_first_run:106 - 检测到首次运行，已有域名但没有子域名
2025-05-29 22:22:02 | INFO     | app.utils.config:register_domain_files_callback:260 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-29 22:22:02 | INFO     | app.utils.config:get_domain_files:222 - 找到 1 个域名文件:
2025-05-29 22:22:02 | INFO     | app.utils.config:get_domain_files:224 -   - project/联想.txt
2025-05-29 22:22:02 | INFO     | app.utils.config:load_domains_from_files:386 - 准备从 1 个域名文件中加载域名
2025-05-29 22:22:02 | INFO     | app.utils.config:load_domains_from_files:402 - 已从 project/联想.txt 加载 1 个域名
2025-05-29 22:22:02 | INFO     | app.utils.config:load_domains_from_files:412 - 总共加载了 1 个唯一域名
2025-05-29 22:22:02 | INFO     | app.core.manager:_start_heartbeat:249 - 心跳监控已启动，间隔: 60秒
2025-05-29 22:22:02 | INFO     | app.core.manager:start_monitoring:398 - 发送启动确认通知
2025-05-29 22:22:02 | INFO     | app.notifiers.notifier:_send_request:225 - 微信通知发送成功
2025-05-29 22:22:02 | INFO     | app.core.manager:_send_alive_notification:376 - 已发送存活确认通知
2025-05-29 22:22:02 | INFO     | app.core.manager:start_monitoring:403 - 检测到首次运行，将进行初始化扫描
2025-05-29 22:22:02 | INFO     | app.core.manager:start_monitoring:408 - 首次运行，暂时禁用通知
2025-05-29 22:22:02 | INFO     | app.notifiers.notifier:start:81 - 通知功能已禁用
2025-05-29 22:22:02 | INFO     | app.core.manager:start_monitoring:419 - 开始对 1 个域名进行初始扫描...
2025-05-29 22:22:02 | INFO     | app.core.manager:start_monitoring:428 - 正在使用crt.sh查询 lenovo.com 的子域名...
2025-05-29 22:22:02 | INFO     | app.core.crtsh_monitor:check_domain:229 - 正在查询crt.sh获取 lenovo.com 的子域名...
2025-05-29 22:22:15 | INFO     | app.core.crtsh_monitor:query_crt_sh:126 - 正在查询crt.sh: lenovo.com (尝试 1/5)
