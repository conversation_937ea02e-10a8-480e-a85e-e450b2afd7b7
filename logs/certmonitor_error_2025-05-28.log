2025-05-28 23:16:24 | INFO     | app.utils.logger:setup_logging:51 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-28 23:16:24 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 10.211.55.21:53306...
2025-05-28 23:16:24 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-28 23:16:24 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-28 23:16:24 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-28 23:16:24 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-28 23:16:24 | INFO     | app.core.manager:_check_first_run:79 - 检测到首次运行，没有找到已监控的域名
2025-05-28 23:16:24 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-28 23:16:24 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 0 个
2025-05-28 23:16:24 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:68 - 更新CT Log监控域名: 0 个
2025-05-28 23:16:24 | WARNING  | app.core.manager:list_domains:408 - 监控列表为空
2025-05-28 23:16:24 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt, 111.txt
2025-05-28 23:16:24 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-28 23:16:24 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-28 23:16:24 | WARNING  | app.utils.config:get_domain_files:229 - 未找到任何域名文件
2025-05-28 23:16:24 | INFO     | app.utils.config:load_domains_from_files:389 - 准备从 0 个域名文件中加载域名
2025-05-28 23:16:24 | INFO     | app.utils.config:load_domains_from_files:415 - 总共加载了 0 个唯一域名
2025-05-28 23:16:24 | WARNING  | app.core.manager:list_domains:408 - 监控列表为空
2025-05-28 23:16:24 | INFO     | app.core.manager:start_monitoring:215 - 检测到首次运行，将进行初始化扫描
2025-05-28 23:16:24 | INFO     | app.core.manager:start_monitoring:220 - 首次运行，暂时禁用通知
2025-05-28 23:16:24 | INFO     | app.notifiers.notifier:start:81 - 通知功能已禁用
2025-05-28 23:16:24 | INFO     | app.core.manager:start_monitoring:246 - 恢复通知设置
2025-05-28 23:16:24 | INFO     | app.core.crtsh_monitor:start:98 - crt.sh监控已启动，间隔: 24.0小时
2025-05-28 23:16:24 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:170 - 开始持续监控CT日志: Let's Encrypt Oak 2025h1
2025-05-28 23:16:24 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:170 - 开始持续监控CT日志: Let's Encrypt Oak 2025h2
2025-05-28 23:16:24 | INFO     | app.core.ctlog_monitor:start:138 - CT Log监控已启动，监控 3 个日志
2025-05-28 23:16:24 | WARNING  | app.core.crtsh_monitor:check_all_domains:290 - 监控列表为空
2025-05-28 23:16:24 | INFO     | app.core.crtsh_monitor:_monitoring_loop:319 - 下次crt.sh查询时间: 2025-05-29 23:16:24
2025-05-28 23:16:24 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt, 111.txt
2025-05-28 23:16:24 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-28 23:16:24 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:170 - 开始持续监控CT日志: Cloudflare Nimbus 2025
2025-05-28 23:16:24 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-28 23:16:24 | INFO     | app.utils.config:start_domain_files_monitor:380 - 域名文件监控已启动，检查间隔: 60分钟
2025-05-28 23:16:25 | INFO     | app.core.manager:start_monitoring:269 - 域名文件动态加载已启动
2025-05-28 23:16:25 | INFO     | app.core.manager:start_monitoring:271 - 所有监控器已启动
2025-05-28 23:16:25 | WARNING  | app.utils.config:get_domain_files:229 - 未找到任何域名文件
2025-05-28 23:16:26 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:179 - Let's Encrypt Oak 2025h1: 从位置 986600532 开始监控
2025-05-28 23:16:27 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:234 - Let's Encrypt Oak 2025h1: 处理 12 个新条目，从 986600532 开始 (总大小: 986600544)
2025-05-28 23:16:27 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:179 - Let's Encrypt Oak 2025h2: 从位置 579342701 开始监控
2025-05-28 23:16:28 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:234 - Let's Encrypt Oak 2025h2: 处理 50 个新条目，从 579342701 开始 (总大小: 579342909)
2025-05-28 23:16:28 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600532: wedidsomething.com, wedidsomething.com, www.wedidsomething.com
2025-05-28 23:16:33 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579342701: 证书数据不完整 (需要7199440字节，只有1117字节)
2025-05-28 23:16:35 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579342702: 证书数据不完整 (需要2865896字节，只有795字节)
2025-05-28 23:16:38 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579342703: 证书数据不完整 (需要7199440字节，只有1044字节)
2025-05-28 23:16:38 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600533: lj89go.com, lj89go.com, *.lj89go.com
2025-05-28 23:16:39 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:179 - Cloudflare Nimbus 2025: 从位置 1614280462 开始监控
2025-05-28 23:16:43 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600534: lofts34.com, lofts34.com
2025-05-28 23:16:45 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600535: livesolimar.com, livesolimar.com
2025-05-28 23:16:46 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #579342704: stcygwww.admin.linguaperfecta.com, stcygwww.admin.linguaperfecta.com
2025-05-28 23:16:46 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600536: www.sa4567.com, sa4567.com, www.sa4567.com
2025-05-28 23:16:50 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #986600537: 证书数据不完整 (需要3904735字节，只有624字节)
2025-05-28 23:16:51 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #579342705: portal-magazine.com, portal-magazine.com
2025-05-28 23:16:51 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:234 - Cloudflare Nimbus 2025: 处理 10 个新条目，从 1614280462 开始 (总大小: 1614280472)
2025-05-28 23:16:53 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600538: marieventerhairsalon.com, marieventerhairsalon.com
2025-05-28 23:16:53 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579342706: 证书数据不完整 (需要2865896字节，只有1065字节)
2025-05-28 23:16:54 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #986600539: 证书数据不完整 (需要2124255字节，只有664字节)
2025-05-28 23:16:55 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1614280462: 证书数据不完整 (需要2865896字节，只有883字节)
2025-05-28 23:16:55 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600540: lucillekhornakphotography.com, lucillekhornakphotography.com
2025-05-28 23:16:58 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600541: simplegmailscreen.com, simplegmailscreen.com, *.simplegmailscreen.com
2025-05-28 23:16:59 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600542: caitlinmo.store, caitlinmo.store, *.caitlinmo.store
2025-05-28 23:17:01 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600543: lehighvalleyhotel.com, lehighvalleyhotel.com
2025-05-28 23:17:01 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:300 - Let's Encrypt Oak 2025h1: 处理了 12 个条目，匹配 0 个，跳过错误 0 个
2025-05-28 23:17:14 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1614280463: 证书数据不完整 (需要2865896字节，只有1057字节)
2025-05-28 23:17:14 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579342707: 证书数据不完整 (需要2865896字节，只有1033字节)
2025-05-28 23:17:16 | DEBUG    | app.core.ctlog_monitor:_process_entry:337 - 条目 #1614280464: 证书长度异常 (16670732 字节)
2025-05-28 23:17:17 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579342708: 证书数据不完整 (需要2865896字节，只有1071字节)
2025-05-28 23:17:42 | INFO     | app.utils.logger:setup_logging:51 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-28 23:17:42 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 10.211.55.21:53306...
2025-05-28 23:17:42 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-28 23:17:43 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-28 23:17:43 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-28 23:17:43 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-28 23:17:43 | INFO     | app.core.manager:_check_first_run:79 - 检测到首次运行，没有找到已监控的域名
2025-05-28 23:17:43 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-28 23:17:43 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 0 个
2025-05-28 23:17:43 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:68 - 更新CT Log监控域名: 0 个
2025-05-28 23:17:43 | WARNING  | app.core.manager:list_domains:408 - 监控列表为空
2025-05-28 23:17:43 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt
2025-05-28 23:17:43 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-28 23:17:43 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-28 23:17:43 | WARNING  | app.utils.config:get_domain_files:229 - 未找到任何域名文件
2025-05-28 23:17:43 | INFO     | app.utils.config:load_domains_from_files:389 - 准备从 0 个域名文件中加载域名
2025-05-28 23:17:43 | INFO     | app.utils.config:load_domains_from_files:415 - 总共加载了 0 个唯一域名
2025-05-28 23:17:43 | WARNING  | app.core.manager:list_domains:408 - 监控列表为空
2025-05-28 23:17:43 | INFO     | app.core.manager:start_monitoring:215 - 检测到首次运行，将进行初始化扫描
2025-05-28 23:17:43 | INFO     | app.core.manager:start_monitoring:220 - 首次运行，暂时禁用通知
2025-05-28 23:17:43 | INFO     | app.notifiers.notifier:start:81 - 通知功能已禁用
2025-05-28 23:17:43 | INFO     | app.core.manager:start_monitoring:246 - 恢复通知设置
2025-05-28 23:17:43 | INFO     | app.core.crtsh_monitor:start:98 - crt.sh监控已启动，间隔: 24.0小时
2025-05-28 23:17:43 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:170 - 开始持续监控CT日志: Let's Encrypt Oak 2025h1
2025-05-28 23:17:43 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:170 - 开始持续监控CT日志: Let's Encrypt Oak 2025h2
2025-05-28 23:17:43 | WARNING  | app.core.crtsh_monitor:check_all_domains:290 - 监控列表为空
2025-05-28 23:17:43 | INFO     | app.core.ctlog_monitor:start:138 - CT Log监控已启动，监控 3 个日志
2025-05-28 23:17:43 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt
2025-05-28 23:17:43 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:170 - 开始持续监控CT日志: Cloudflare Nimbus 2025
2025-05-28 23:17:43 | INFO     | app.core.crtsh_monitor:_monitoring_loop:319 - 下次crt.sh查询时间: 2025-05-29 23:17:43
2025-05-28 23:17:43 | INFO     | app.utils.config:start_domain_files_monitor:380 - 域名文件监控已启动，检查间隔: 60分钟
2025-05-28 23:17:43 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-28 23:17:43 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-28 23:17:43 | INFO     | app.core.manager:start_monitoring:269 - 域名文件动态加载已启动
2025-05-28 23:17:43 | WARNING  | app.utils.config:get_domain_files:229 - 未找到任何域名文件
2025-05-28 23:17:43 | INFO     | app.core.manager:start_monitoring:271 - 所有监控器已启动
2025-05-28 23:17:46 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:179 - Cloudflare Nimbus 2025: 从位置 1614280462 开始监控
2025-05-28 23:17:47 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:179 - Let's Encrypt Oak 2025h2: 从位置 579348610 开始监控
2025-05-28 23:17:48 | INFO     | app.core.ctlog_monitor:_monitor_ct_log:179 - Let's Encrypt Oak 2025h1: 从位置 986600580 开始监控
2025-05-28 23:17:48 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:234 - Cloudflare Nimbus 2025: 处理 10 个新条目，从 1614280462 开始 (总大小: 1614280472)
2025-05-28 23:17:49 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:234 - Let's Encrypt Oak 2025h2: 处理 50 个新条目，从 579348610 开始 (总大小: 579348725)
2025-05-28 23:17:49 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:234 - Let's Encrypt Oak 2025h1: 处理 10 个新条目，从 986600580 开始 (总大小: 986600590)
2025-05-28 23:17:50 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579348610: 证书数据不完整 (需要2865896字节，只有944字节)
2025-05-28 23:17:50 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600580: acad.com.ua, acad.com.ua, *.acad.com.ua
2025-05-28 23:17:52 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579348611: 证书数据不完整 (需要7199440字节，只有1052字节)
2025-05-28 23:17:53 | DEBUG    | app.core.ctlog_monitor:_process_entry:337 - 条目 #986600581: 证书长度异常 (16670732 字节)
2025-05-28 23:17:56 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600582: r1748445285z25a3851d.dc1.monitor.radiantlock.org
2025-05-28 23:17:57 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600583: v2-wasmrp.pages.dev, v2-wasmrp.pages.dev, *.v2-wasmrp.pages.dev
2025-05-28 23:17:58 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579348612: 证书数据不完整 (需要7199440字节，只有1057字节)
2025-05-28 23:17:58 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600584: 343744.com, 343744.com
2025-05-28 23:17:58 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1614280462: 证书数据不完整 (需要2865896字节，只有883字节)
2025-05-28 23:17:59 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #579348613: les-fonds-perdus.fr, *.les-fonds-perdus.fr, les-fonds-perdus.fr
2025-05-28 23:18:00 | DEBUG    | app.core.ctlog_monitor:_process_entry:337 - 条目 #986600585: 证书长度异常 (14790300 字节)
2025-05-28 23:18:00 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1614280463: 证书数据不完整 (需要2865896字节，只有1057字节)
2025-05-28 23:18:02 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600586: btbk5xpxrth0wbtrkfygmch2v1.excl.cloud, btbk5xpxrth0wbtrkfygmch2v1.excl.cloud, www.btbk5xpxrth0wbtrkfygmch2v1.excl.cloud
2025-05-28 23:18:04 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600587: www.futurereadyfriday.live, www.futurereadyfriday.live, futurereadyfriday.live
2025-05-28 23:18:05 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600588: sleepaas.net, sleepaas.net, *.sleepaas.net
2025-05-28 23:18:07 | DEBUG    | app.core.ctlog_monitor:_process_entry:337 - 条目 #1614280464: 证书长度异常 (16670732 字节)
2025-05-28 23:18:07 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #986600589: laurenfritsch.com, laurenfritsch.com
2025-05-28 23:18:07 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:300 - Let's Encrypt Oak 2025h1: 处理了 10 个条目，匹配 0 个，跳过错误 0 个
2025-05-28 23:18:11 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1614280465: 证书数据不完整 (需要2865896字节，只有1033字节)
2025-05-28 23:18:13 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1614280466: 证书数据不完整 (需要7199440字节，只有1067字节)
2025-05-28 23:18:15 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1614280467: 证书数据不完整 (需要9471864字节，只有664字节)
2025-05-28 23:18:19 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [CLOUDFLARE] 接收到证书 #1614280468: hongjiuchaoshi.com, hongjiuchaoshi.com, *.hongjiuchaoshi.com
2025-05-28 23:18:22 | DEBUG    | app.core.ctlog_monitor:_process_entry:337 - 条目 #1614280469: 证书长度异常 (13122493 字节)
2025-05-28 23:18:26 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1614280470: 证书数据不完整 (需要2809258字节，只有691字节)
2025-05-28 23:18:29 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #1614280471: 证书数据不完整 (需要7199440字节，只有1069字节)
2025-05-28 23:18:29 | DEBUG    | app.core.ctlog_monitor:_check_new_entries:300 - Cloudflare Nimbus 2025: 处理了 10 个条目，匹配 0 个，跳过错误 0 个
2025-05-28 23:18:35 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #579348614: boekonomi.se, boekonomi.se, www.boekonomi.se
2025-05-28 23:18:36 | DEBUG    | app.core.ctlog_monitor:_process_entry:347 - 条目 #579348615: 证书数据不完整 (需要7199440字节，只有837字节)
2025-05-28 23:18:37 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #579348616: www.palovpn.bfjd.com, www.palovpn.bfjd.com
2025-05-28 23:18:45 | DEBUG    | app.core.ctlog_monitor:_process_entry:405 - [LETSENCRYPT] 接收到证书 #579348617: reliablegenericproductsforeveryone.com, reliablegenericproductsforeveryone.com, *.reliablegenericproductsforeveryone.com
2025-05-28 23:18:50 | INFO     | app.utils.logger:setup_logging:51 - 日志系统已初始化，级别: DEBUG, 文件模式: 按日期分割
2025-05-28 23:18:50 | INFO     | app.db.pool:_create_pool:65 - 尝试连接到MySQL服务器 10.211.55.21:53306...
2025-05-28 23:18:51 | INFO     | app.db.pool:_create_pool:90 - 正在创建数据库连接池，池大小: 5...
2025-05-28 23:18:51 | INFO     | app.db.pool:_create_pool:101 - 数据库连接池创建成功
2025-05-28 23:18:51 | INFO     | app.db.pool:_init_tables:172 - 数据库表初始化成功
2025-05-28 23:18:51 | INFO     | app.core.ctlog_monitor:_load_ct_logs_config:103 - 加载了 3 个CT日志配置
2025-05-28 23:18:51 | INFO     | app.core.manager:_check_first_run:79 - 检测到首次运行，没有找到已监控的域名
2025-05-28 23:18:51 | INFO     | app.utils.config:register_domain_files_callback:263 - 已注册域名文件变化回调函数: _handle_domain_files_change
2025-05-28 23:18:51 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 0 个
2025-05-28 23:18:51 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:68 - 更新CT Log监控域名: 0 个
2025-05-28 23:18:51 | WARNING  | app.core.manager:list_domains:408 - 监控列表为空
2025-05-28 23:18:51 | DEBUG    | app.utils.config:get_domain_files:190 - 域名文件搜索路径: domains.txt, project/联想.txt
2025-05-28 23:18:51 | DEBUG    | app.utils.config:get_domain_files:191 - 域名文件扩展名: .txt, .list, .domains
2025-05-28 23:18:51 | DEBUG    | app.utils.config:get_domain_files:192 - 是否递归搜索: True
2025-05-28 23:18:51 | INFO     | app.utils.config:get_domain_files:225 - 找到 1 个域名文件:
2025-05-28 23:18:51 | INFO     | app.utils.config:get_domain_files:227 -   - project/联想.txt
2025-05-28 23:18:51 | INFO     | app.utils.config:load_domains_from_files:389 - 准备从 1 个域名文件中加载域名
2025-05-28 23:18:51 | INFO     | app.utils.config:load_domains_from_files:405 - 已从 project/联想.txt 加载 1 个域名
2025-05-28 23:18:51 | INFO     | app.utils.config:load_domains_from_files:415 - 总共加载了 1 个唯一域名
2025-05-28 23:18:51 | INFO     | app.core.manager:add_domain:296 - 已添加 lenovo.com 到监控列表
2025-05-28 23:18:51 | INFO     | app.core.ctlog_monitor:set_monitored_domains:112 - 设置监控域名: 1 个
2025-05-28 23:18:51 | DEBUG    | app.core.manager:_update_ctlog_monitored_domains:68 - 更新CT Log监控域名: 1 个
2025-05-28 23:18:51 | INFO     | app.core.manager:add_domain:306 - 首次运行，暂时禁用通知
2025-05-28 23:18:51 | INFO     | app.core.manager:add_domain:312 - 正在使用crt.sh查询 lenovo.com 的子域名...
2025-05-28 23:18:51 | INFO     | app.core.crtsh_monitor:check_domain:229 - 正在查询crt.sh获取 lenovo.com 的子域名...
