2025-05-28 23:38:13 - INFO - 日志系统已初始化，级别: INFO, 文件: logs/certmonitor.log
2025-05-28 23:38:13 - ERROR - 程序异常
Traceback (most recent call last):
  File "/app/certmonitor.py", line 313, in main
    start_monitoring(args)
  File "/app/certmonitor.py", line 181, in start_monitoring
    manager = get_monitor_manager(args.config)
  File "/app/app/core/manager.py", line 362, in get_monitor_manager
    _monitor_manager_instance = MonitorManager(config_file)
  File "/app/app/core/manager.py", line 38, in __init__
    self.db = get_db_instance(config_file)
  File "/app/app/db/manager.py", line 191, in get_db_instance
    _db_instance = DatabaseManager(config_file)
  File "/app/app/db/manager.py", line 33, in __init__
    self.pool = get_pool_instance(config_file)
  File "/app/app/db/pool.py", line 237, in get_pool_instance
    _pool_instance = DatabasePool(config_file)
  File "/app/app/db/pool.py", line 42, in __init__
    self.port = self.config.getint('database', 'mysql_port', fallback=3306)
  File "/usr/local/lib/python3.9/configparser.py", line 818, in getint
    return self._get_conv(section, option, int, raw=raw, vars=vars,
  File "/usr/local/lib/python3.9/configparser.py", line 808, in _get_conv
    return self._get(section, conv, option, raw=raw, vars=vars,
  File "/usr/local/lib/python3.9/configparser.py", line 803, in _get
    return conv(self.get(section, option, **kwargs))
ValueError: invalid literal for int() with base 10: '{DB_PORT}'
