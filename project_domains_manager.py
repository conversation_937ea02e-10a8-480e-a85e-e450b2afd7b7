#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Project域名管理工具 - 用于在project目录中添加、修改和删除域名文件
"""

import os
import time
import random
import logging
import argparse
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def generate_random_domain():
    """生成随机域名"""
    tlds = ['.com', '.org', '.net', '.io', '.app', '.dev']
    chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    length = random.randint(5, 10)
    domain = ''.join(random.choice(chars) for _ in range(length))
    tld = random.choice(tlds)
    return domain + tld

def create_domain_file(file_path, num_domains=5):
    """创建域名文件"""
    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    with open(file_path, 'w') as f:
        f.write(f"# 域名文件: {os.path.basename(file_path)}\n")
        f.write(f"# 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("# 每行一个域名，以#开头的行将被忽略\n\n")
        
        for _ in range(num_domains):
            domain = generate_random_domain()
            f.write(f"{domain}\n")
    
    logging.info(f"已创建域名文件: {file_path}，包含 {num_domains} 个随机域名")

def add_domains_to_file(file_path, num_domains=3):
    """向域名文件添加域名"""
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return
    
    with open(file_path, 'a') as f:
        f.write(f"\n# 添加于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        for _ in range(num_domains):
            domain = generate_random_domain()
            f.write(f"{domain}\n")
    
    logging.info(f"已向 {file_path} 添加 {num_domains} 个随机域名")

def remove_domains_from_file(file_path, num_domains=None, percentage=50):
    """从域名文件中移除域名"""
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return
    
    # 读取文件内容
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    # 找出所有域名行（非注释行）
    domain_lines = []
    for i, line in enumerate(lines):
        line = line.strip()
        if line and not line.startswith('#'):
            domain_lines.append(i)
    
    if not domain_lines:
        logging.warning(f"文件中没有域名: {file_path}")
        return
    
    # 确定要移除的域名数量
    if num_domains is None:
        num_domains = max(1, int(len(domain_lines) * percentage / 100))
    else:
        num_domains = min(num_domains, len(domain_lines))
    
    # 随机选择要移除的域名行
    to_remove = random.sample(domain_lines, num_domains)
    
    # 创建新的文件内容，排除要移除的行
    new_lines = []
    for i, line in enumerate(lines):
        if i not in to_remove:
            new_lines.append(line)
    
    # 写入文件
    with open(file_path, 'w') as f:
        f.writelines(new_lines)
    
    logging.info(f"已从 {file_path} 中移除 {num_domains} 个域名")

def delete_file(file_path):
    """删除域名文件"""
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return
    
    os.remove(file_path)
    logging.info(f"已删除域名文件: {file_path}")

def list_domain_files(directory='project'):
    """列出目录中的所有域名文件"""
    if not os.path.exists(directory):
        logging.error(f"目录不存在: {directory}")
        return []
    
    domain_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.txt'):
                domain_files.append(os.path.join(root, file))
    
    return domain_files

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Project域名管理工具")
    subparsers = parser.add_subparsers(dest='command', help='子命令')
    
    # 创建域名文件
    create_parser = subparsers.add_parser('create', help='创建域名文件')
    create_parser.add_argument('filename', help='文件名')
    create_parser.add_argument('-n', '--num', type=int, default=5, help='域名数量')
    
    # 添加域名
    add_parser = subparsers.add_parser('add', help='添加域名')
    add_parser.add_argument('filename', help='文件名')
    add_parser.add_argument('-n', '--num', type=int, default=3, help='域名数量')
    
    # 移除域名
    remove_parser = subparsers.add_parser('remove', help='移除域名')
    remove_parser.add_argument('filename', help='文件名')
    remove_parser.add_argument('-n', '--num', type=int, help='域名数量')
    remove_parser.add_argument('-p', '--percentage', type=int, default=50, help='移除的域名百分比')
    
    # 删除文件
    delete_parser = subparsers.add_parser('delete', help='删除域名文件')
    delete_parser.add_argument('filename', help='文件名')
    
    # 列出域名文件
    list_parser = subparsers.add_parser('list', help='列出域名文件')
    
    args = parser.parse_args()
    
    # 处理命令
    if args.command == 'create':
        file_path = os.path.join('project', args.filename)
        if not file_path.endswith('.txt'):
            file_path += '.txt'
        create_domain_file(file_path, args.num)
    
    elif args.command == 'add':
        file_path = os.path.join('project', args.filename)
        if not file_path.endswith('.txt'):
            file_path += '.txt'
        add_domains_to_file(file_path, args.num)
    
    elif args.command == 'remove':
        file_path = os.path.join('project', args.filename)
        if not file_path.endswith('.txt'):
            file_path += '.txt'
        remove_domains_from_file(file_path, args.num, args.percentage)
    
    elif args.command == 'delete':
        file_path = os.path.join('project', args.filename)
        if not file_path.endswith('.txt'):
            file_path += '.txt'
        delete_file(file_path)
    
    elif args.command == 'list':
        domain_files = list_domain_files()
        if domain_files:
            logging.info(f"找到 {len(domain_files)} 个域名文件:")
            for file_path in domain_files:
                logging.info(f"  - {file_path}")
        else:
            logging.warning("未找到任何域名文件")
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
