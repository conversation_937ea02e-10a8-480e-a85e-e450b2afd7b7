#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库修复脚本 - 修复数据库结构
"""

from loguru import logger
import mysql.connector
from mysql.connector import Error as MySQLError
from configparser import ConfigParser

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def get_db_config(config_file='app/config/config.ini'):
    """获取数据库配置"""
    config = ConfigParser()
    config.read(config_file)
    
    return {
        'host': config.get('database', 'mysql_host', fallback='localhost'),
        'port': config.getint('database', 'mysql_port', fallback=3306),
        'user': config.get('database', 'mysql_user', fallback='root'),
        'password': config.get('database', 'mysql_password', fallback=''),
        'database': config.get('database', 'mysql_database', fallback='certmonitor')
    }

def fix_database():
    """修复数据库结构"""
    # 设置日志
    setup_logging()
    
    # 获取数据库配置
    db_config = get_db_config()
    
    try:
        # 连接到数据库
        logger.info(f"正在连接到数据库 {db_config['host']}:{db_config['port']}...")
        conn = mysql.connector.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database']
        )
        cursor = conn.cursor()
        
        # 检查 subdomains 表是否存在 first_seen 列
        logger.info("检查 subdomains 表是否存在 first_seen 列...")
        cursor.execute("SHOW COLUMNS FROM subdomains LIKE 'first_seen'")
        result = cursor.fetchone()
        
        if not result:
            # 添加 first_seen 列
            logger.info("subdomains 表中不存在 first_seen 列，正在添加...")
            cursor.execute("ALTER TABLE subdomains ADD COLUMN first_seen DATE NULL")
            cursor.execute("CREATE INDEX idx_first_seen ON subdomains(first_seen)")
            conn.commit()
            logger.info("成功添加 first_seen 列")
        else:
            logger.info("subdomains 表中已存在 first_seen 列，无需修复")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        logger.info("数据库修复完成")
        return True
        
    except MySQLError as e:
        logger.error(f"数据库修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    fix_database()
