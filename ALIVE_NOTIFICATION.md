# CertMonitor 存活通知功能

## 🎯 功能说明

CertMonitor 内置了存活通知功能，会定期通过企业微信发送存活确认消息，让您随时了解监控脚本的运行状态。

## ⚙️ 配置方法

### 1. 配置文件设置

在 `app/config/config.ini` 中配置存活通知间隔：

```ini
[general]
# 存活通知间隔（小时），设置为0禁用存活通知
alive_notification_hours = 6
```

### 2. 通知间隔说明

- **默认值**: 6小时
- **最小值**: 1小时（建议不要设置太频繁）
- **禁用**: 设置为 0 可禁用存活通知
- **推荐值**: 6-12小时

## 📱 通知内容

存活通知包含以下信息：

```
🟢 CertMonitor 存活确认

⏱️ 运行时间: 2小时30分钟
🎯 监控域名: 5个
📅 时间: 05-29 21:44

📡 CT日志:✅ | crt.sh:✅
💡 下次确认: 6小时后
```

### 信息说明

- **运行时间**: 脚本连续运行的时间
- **监控域名**: 当前监控的域名数量
- **时间**: 当前检查时间
- **模块状态**: CT日志监控和crt.sh监控的运行状态
- **下次确认**: 下次发送存活通知的时间

## 🚀 使用方法

### 1. 启动监控（自动启用存活通知）

```bash
# 启动监控，自动启用存活通知
python3 certmonitor.py -m
```

### 2. 存活通知时机

- **启动时**: 监控启动后立即发送一次确认通知
- **定期发送**: 按配置的间隔定期发送
- **自动管理**: 无需手动干预

### 3. 查看状态

```bash
# 查看监控域名和简单状态
python3 certmonitor.py -l
```

输出示例：
```
[*] 监控的域名列表 (共 1 个):

  - lenovo.com (子域名数量: 0, 来源: manual, 添加时间: 2025-05-29 20:45:23)

[*] 监控状态: ✅ 运行中 (PID: 12345)
```

## 🔧 配置示例

### 不同使用场景的配置建议

#### 1. 生产环境（推荐）
```ini
alive_notification_hours = 12
```
- 每12小时发送一次存活确认
- 平衡了监控需求和通知频率

#### 2. 测试环境
```ini
alive_notification_hours = 2
```
- 每2小时发送一次存活确认
- 便于快速验证功能

#### 3. 禁用存活通知
```ini
alive_notification_hours = 0
```
- 完全禁用存活通知
- 只接收新域名发现通知

## 📋 故障排除

### 1. 没有收到存活通知

**检查配置**:
```bash
# 检查配置文件
grep "alive_notification_hours" app/config/config.ini

# 检查微信配置
grep "enabled\|webhook_url" app/config/config.ini
```

**检查日志**:
```bash
# 查看存活通知相关日志
grep "存活" logs/certmonitor_$(date +%Y-%m-%d).log
```

### 2. 通知发送失败

常见原因：
- 企业微信 webhook 地址错误
- 网络连接问题
- 微信机器人被禁用

**解决方法**:
1. 检查 webhook 地址是否正确
2. 测试网络连接
3. 确认微信机器人状态

### 3. 通知频率异常

**检查运行状态**:
```bash
# 查看进程
ps aux | grep certmonitor

# 查看心跳文件
cat status/heartbeat.json
```

## 💡 最佳实践

### 1. 合理设置通知间隔
- **生产环境**: 6-12小时
- **测试环境**: 1-4小时
- **避免过于频繁**: 不建议少于1小时

### 2. 监控告警集成
可以结合其他监控系统：
- 如果超过预期时间没收到存活通知，触发告警
- 设置备用监控机制

### 3. 日志监控
定期检查日志文件：
```bash
# 查看最近的存活通知记录
tail -20 logs/certmonitor_$(date +%Y-%m-%d).log | grep "存活"
```

## 🎉 优势

1. **主动通知**: 无需手动检查，主动发送状态确认
2. **简洁信息**: 包含关键运行信息，一目了然
3. **自动管理**: 启动监控后自动运行，无需额外配置
4. **灵活配置**: 可根据需要调整通知间隔或禁用
5. **故障发现**: 如果停止收到通知，说明脚本可能异常

通过存活通知功能，您可以随时了解 CertMonitor 的运行状态，确保子域名监控服务持续稳定运行！
