#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
彻底禁用标准 logging 模块
"""

import logging
import sys
import os

class LoggingKiller:
    """彻底禁用标准 logging 的类"""
    
    def __init__(self):
        self.kill_logging()
    
    def kill_logging(self):
        """彻底禁用标准 logging"""
        # 1. 禁用 logging 模块
        logging.disable(logging.CRITICAL)
        
        # 2. 获取根日志器并禁用
        root_logger = logging.getLogger()
        root_logger.disabled = True
        root_logger.setLevel(logging.CRITICAL + 1)
        
        # 3. 移除所有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
            try:
                handler.close()
            except:
                pass
        
        root_logger.handlers.clear()
        
        # 4. 重写关键函数
        def dummy_basic_config(*args, **kwargs):
            pass
        
        def dummy_get_logger(name=None):
            logger = logging.Logger(name or 'dummy')
            logger.disabled = True
            logger.setLevel(logging.CRITICAL + 1)
            return logger
        
        def dummy_add_handler(handler):
            pass
        
        def dummy_set_level(level):
            pass
        
        # 5. 替换 logging 模块的关键函数
        logging.basicConfig = dummy_basic_config
        logging.getLogger = dummy_get_logger
        
        # 6. 禁用所有日志级别的函数
        for level in ['debug', 'info', 'warning', 'error', 'critical', 'exception']:
            setattr(logging, level, lambda *args, **kwargs: None)
        
        # 7. 监控并删除可能生成的日志文件
        self.start_file_monitor()
    
    def start_file_monitor(self):
        """启动文件监控，删除可能生成的日志文件"""
        import threading
        import time
        
        def monitor_files():
            while True:
                try:
                    # 删除可能生成的日志文件
                    files_to_remove = [
                        'logs/certmonitor.log',
                        'certmonitor.log',
                        'app.log',
                        'debug.log'
                    ]
                    
                    for file_path in files_to_remove:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            print(f"🗑️  已删除多余的日志文件: {file_path}")
                except Exception:
                    pass
                
                time.sleep(1)  # 每秒检查一次
        
        # 启动后台监控线程
        thread = threading.Thread(target=monitor_files, daemon=True)
        thread.start()

# 创建全局实例，立即生效
_logging_killer = LoggingKiller()

def ensure_logging_killed():
    """确保 logging 被禁用"""
    global _logging_killer
    if _logging_killer is None:
        _logging_killer = LoggingKiller()
    else:
        _logging_killer.kill_logging()

if __name__ == "__main__":
    print("✅ Logging killer 已启动")
    
    # 测试
    import time
    try:
        while True:
            time.sleep(5)
            print("🔍 监控中...")
    except KeyboardInterrupt:
        print("\n⏹️  停止监控")
