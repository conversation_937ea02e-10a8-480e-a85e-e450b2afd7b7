#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库连接池模块 - 提供MySQL数据库连接池
"""

import os
import time
from loguru import logger
import threading
import mysql.connector
from mysql.connector import <PERSON>rror as MySQLError
from mysql.connector.pooling import MySQLConnectionPool
from configparser import ConfigParser

class DatabasePool:
    """数据库连接池类"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """单例模式"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(DatabasePool, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self, config_file='app/config/config.ini'):
        """初始化数据库连接池"""
        # 避免重复初始化
        if self._initialized:
            return

        self.config = ConfigParser()
        self.config.read(config_file)

        # 获取MySQL配置
        self.host = self.config.get('database', 'mysql_host', fallback='localhost')
        self.port = self.config.getint('database', 'mysql_port', fallback=3306)
        self.user = self.config.get('database', 'mysql_user', fallback='root')
        self.password = self.config.get('database', 'mysql_password', fallback='')
        self.database = self.config.get('database', 'mysql_database', fallback='certmonitor')
        self.connect_timeout = self.config.getint('database', 'connect_timeout', fallback=10)
        self.max_retries = self.config.getint('database', 'max_retries', fallback=3)
        self.retry_delay = self.config.getint('database', 'retry_delay', fallback=5)
        self.pool_size = self.config.getint('database', 'pool_size', fallback=5)
        self.pool_name = self.config.get('database', 'pool_name', fallback='certmonitor_pool')

        # 创建连接池
        self._create_pool()

        self._initialized = True

    def _create_pool(self):
        """创建数据库连接池"""
        retries = 0
        last_error = None

        while retries < self.max_retries:
            try:
                # 首先尝试连接到MySQL服务器（不指定数据库）
                logger.info(f"尝试连接到MySQL服务器 {self.host}:{self.port}...")
                conn = mysql.connector.connect(
                    host=self.host,
                    port=self.port,
                    user=self.user,
                    password=self.password,
                    connection_timeout=self.connect_timeout
                )
                cursor = conn.cursor()

                # 检查数据库是否存在
                cursor.execute(f"SHOW DATABASES LIKE '{self.database}'")
                result = cursor.fetchone()

                # 如果数据库不存在，则创建
                if not result:
                    logger.info(f"数据库 {self.database} 不存在，正在创建...")
                    cursor.execute(f"CREATE DATABASE {self.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                    logger.info(f"数据库 {self.database} 创建成功")

                # 关闭连接
                cursor.close()
                conn.close()

                # 创建连接池
                logger.info(f"正在创建数据库连接池，池大小: {self.pool_size}...")
                self.pool = MySQLConnectionPool(
                    pool_name=self.pool_name,
                    pool_size=self.pool_size,
                    host=self.host,
                    port=self.port,
                    user=self.user,
                    password=self.password,
                    database=self.database,
                    connect_timeout=self.connect_timeout
                )
                logger.info(f"数据库连接池创建成功")

                # 初始化数据库表
                self._init_tables()

                return

            except MySQLError as e:
                last_error = e
                retries += 1
                logger.error(f"MySQL连接错误 (尝试 {retries}/{self.max_retries}): {str(e)}")

                if retries < self.max_retries:
                    logger.info(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)

        # 如果所有重试都失败，则抛出异常
        logger.critical(f"无法连接到MySQL数据库，已达到最大重试次数: {self.max_retries}")
        raise MySQLError(f"无法连接到MySQL数据库: {str(last_error)}")

    def _init_tables(self):
        """初始化数据库表"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 创建域名表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS domains (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL UNIQUE,
                source VARCHAR(255),
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX (name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ''')

            # 创建子域名表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS subdomains (
                id INT AUTO_INCREMENT PRIMARY KEY,
                domain_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                source VARCHAR(50),
                discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                first_seen DATE NULL,
                FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
                UNIQUE KEY (domain_id, name),
                INDEX (name),
                INDEX (first_seen)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ''')

            # 创建变更记录表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS changes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                domain_id INT NOT NULL,
                subdomain_id INT NOT NULL,
                action ENUM('add', 'remove') NOT NULL,
                source VARCHAR(50),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notified BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
                FOREIGN KEY (subdomain_id) REFERENCES subdomains(id) ON DELETE CASCADE,
                INDEX (timestamp)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ''')

            conn.commit()
            logger.info("数据库表初始化成功")

        except MySQLError as e:
            logger.error(f"初始化数据库表失败: {str(e)}")
            conn.rollback()
            raise
        finally:
            cursor.close()
            conn.close()

    def get_connection(self):
        """获取数据库连接"""
        try:
            return self.pool.get_connection()
        except MySQLError as e:
            logger.error(f"获取数据库连接失败: {str(e)}")
            # 尝试重新创建连接池
            self._create_pool()
            return self.pool.get_connection()

    def execute_query(self, query, params=None, fetch=False, commit=True):
        """执行SQL查询"""
        conn = None
        cursor = None

        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)

            # 执行查询
            cursor.execute(query, params)

            # 提交事务
            if commit:
                conn.commit()

            # 获取结果
            if fetch:
                return cursor.fetchall()
            elif query.strip().upper().startswith("INSERT"):
                return cursor.lastrowid
            elif query.strip().upper().startswith("SELECT"):
                return cursor.fetchone()
            else:
                return cursor.rowcount

        except MySQLError as e:
            if conn:
                conn.rollback()
            logger.error(f"执行查询失败: {str(e)}")
            raise

        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

# 全局实例
_pool_instance = None

def get_pool_instance(config_file='app/config/config.ini'):
    """获取数据库连接池实例"""
    global _pool_instance
    if _pool_instance is None:
        _pool_instance = DatabasePool(config_file)
    return _pool_instance
