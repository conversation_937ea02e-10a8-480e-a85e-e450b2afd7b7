#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库管理模块 - 处理数据库操作
"""

from loguru import logger
import threading
from mysql.connector import Error as MySQLError
from app.db.pool import get_pool_instance

class DatabaseManager:
    """数据库管理类"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """单例模式"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(DatabaseManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self, config_file='app/config/config.ini'):
        """初始化数据库管理器"""
        # 避免重复初始化
        if self._initialized:
            return

        self.pool = get_pool_instance(config_file)
        self._initialized = True

    def add_domain(self, domain_name, source=None):
        """添加新域名到监控列表"""
        try:
            query = "INSERT INTO domains (name, source) VALUES (%s, %s)"
            return self.pool.execute_query(query, (domain_name.lower(), source))
        except MySQLError as e:
            # 如果是重复键错误，则获取已存在的域名ID
            if hasattr(e, 'errno') and e.errno == 1062:  # ER_DUP_ENTRY
                logger.debug(f"域名已存在: {domain_name}")
                return self.get_domain_id(domain_name)
            raise

    def get_domain_id(self, domain_name):
        """通过域名获取ID"""
        query = "SELECT id FROM domains WHERE name = %s"
        result = self.pool.execute_query(query, (domain_name.lower(),), commit=False)

        if result:
            return result['id']
        return None

    def remove_domain(self, domain_name):
        """从监控列表中移除域名"""
        domain_name = domain_name.lower()
        domain_id = self.get_domain_id(domain_name)

        if not domain_id:
            logger.debug(f"数据库中未找到域名: {domain_name}")
            return False

        # 获取子域名数量
        subdomains = self.get_subdomains(domain_id)
        subdomain_count = len(subdomains) if subdomains else 0

        # 记录删除操作
        logger.debug(f"准备从数据库中删除域名: {domain_name} (ID: {domain_id}, 子域名数量: {subdomain_count})")

        # 删除域名（级联删除子域名和变更记录）
        query = "DELETE FROM domains WHERE id = %s"
        rows_affected = self.pool.execute_query(query, (domain_id,))

        if rows_affected > 0:
            logger.debug(f"成功从数据库中删除域名: {domain_name} (ID: {domain_id}, 子域名数量: {subdomain_count})")
            return True
        else:
            logger.debug(f"从数据库中删除域名失败: {domain_name} (ID: {domain_id})")
            return False

    def get_all_domains(self):
        """获取所有监控的域名"""
        query = """
        SELECT d.id, d.name, d.source, d.added_at, d.last_updated, COUNT(s.id) as subdomain_count
        FROM domains d
        LEFT JOIN subdomains s ON d.id = s.domain_id
        GROUP BY d.id
        ORDER BY d.name
        """

        return self.pool.execute_query(query, fetch=True, commit=False)

    def add_subdomain(self, domain_id, subdomain_name, source=None, first_seen=None):
        """添加新的子域名，返回变更ID"""
        try:
            # 插入子域名
            query = "INSERT INTO subdomains (domain_id, name, source, first_seen) VALUES (%s, %s, %s, %s)"
            subdomain_id = self.pool.execute_query(query, (domain_id, subdomain_name.lower(), source, first_seen))

            # 更新域名的最后更新时间
            update_query = "UPDATE domains SET last_updated = CURRENT_TIMESTAMP WHERE id = %s"
            self.pool.execute_query(update_query, (domain_id,))

            # 记录变更并返回变更ID
            change_id = self.add_change_record(domain_id, subdomain_id, 'add', source)

            return change_id
        except MySQLError as e:
            # 如果是重复键错误，则返回None表示没有新增
            if hasattr(e, 'errno') and e.errno == 1062:  # ER_DUP_ENTRY
                logger.debug(f"子域名已存在: {subdomain_name}")
                return None
            raise

    def get_subdomain_id(self, domain_id, subdomain_name):
        """获取子域名ID"""
        query = "SELECT id FROM subdomains WHERE domain_id = %s AND name = %s"
        result = self.pool.execute_query(query, (domain_id, subdomain_name.lower()), commit=False)

        if result:
            return result['id']
        return None

    def get_subdomains(self, domain_id):
        """获取指定域名的所有子域名"""
        query = """
        SELECT id, name, source, discovered_at
        FROM subdomains
        WHERE domain_id = %s
        ORDER BY name
        """

        return self.pool.execute_query(query, (domain_id,), fetch=True, commit=False)

    def is_subdomain_exists(self, domain_id, subdomain_name):
        """检查子域名是否已存在"""
        return self.get_subdomain_id(domain_id, subdomain_name.lower()) is not None

    def add_change_record(self, domain_id, subdomain_id, action, source=None):
        """添加变更记录，返回变更ID"""
        query = "INSERT INTO changes (domain_id, subdomain_id, action, source) VALUES (%s, %s, %s, %s)"
        change_id = self.pool.execute_query(query, (domain_id, subdomain_id, action, source))
        return change_id

    def get_recent_changes(self, limit=100):
        """获取最近的变更记录"""
        query = """
        SELECT c.id, d.name as domain, s.name as subdomain, c.action, c.source, c.timestamp, c.notified
        FROM changes c
        JOIN domains d ON c.domain_id = d.id
        JOIN subdomains s ON c.subdomain_id = s.id
        ORDER BY c.timestamp DESC
        LIMIT %s
        """

        return self.pool.execute_query(query, (limit,), fetch=True, commit=False)

    def get_unnotified_changes(self):
        """获取未通知的变更记录"""
        query = """
        SELECT c.id, d.name as domain, s.name as subdomain, c.action, c.source, c.timestamp
        FROM changes c
        JOIN domains d ON c.domain_id = d.id
        JOIN subdomains s ON c.subdomain_id = s.id
        WHERE c.notified = FALSE
        ORDER BY c.timestamp ASC
        """

        return self.pool.execute_query(query, fetch=True, commit=False)

    def mark_changes_as_notified(self, change_ids):
        """将变更记录标记为已通知"""
        if not change_ids:
            return 0

        placeholders = ', '.join(['%s'] * len(change_ids))
        query = f"UPDATE changes SET notified = TRUE WHERE id IN ({placeholders})"

        return self.pool.execute_query(query, change_ids)

# 全局实例
_db_instance = None

def get_db_instance(config_file='app/config/config.ini'):
    """获取数据库管理器实例"""
    global _db_instance
    if _db_instance is None:
        _db_instance = DatabaseManager(config_file)
    return _db_instance
