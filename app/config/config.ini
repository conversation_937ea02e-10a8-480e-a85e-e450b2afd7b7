# CertMonitor 配置文件 - 简化版

[general]
# 日志级别: DEBUG, INFO, WARNING, ERROR
log_level = INFO
# 存活通知间隔（小时），0=禁用
alive_notification_hours = 6

[database]
# MySQL数据库配置
mysql_host = ************
mysql_port = 53306
mysql_user = root
mysql_password = g1wXuNKKl3CoZo5z7R
mysql_database = certmonitor

[crtsh]
# crt.sh查询间隔（小时）
interval = 24

[letsencrypt]
# Let's Encrypt CT日志监控
enabled = true
oak_2025h1_url = https://oak.ct.letsencrypt.org/2025h1/
oak_2025h2_url = https://oak.ct.letsencrypt.org/2025h2/
check_interval = 180

[cloudflare]
# Cloudflare CT日志监控
enabled = true
nimbus_2025_url = https://ct.cloudflare.com/logs/nimbus2025/
check_interval = 180

[wechat]
# 企业微信通知
enabled = true
webhook_url = https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e4fd3203-0828-4545-9632-5f91297d51ff
