[general]
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
log_level = DEBUG
# 日志文件路径（实际文件名会自动添加日期，如：certmonitor_2025-05-27.log）
log_file = logs/certmonitor.log
# 是否在控制台显示彩色输出
color_output = true
# 存活通知间隔（小时），设置为0禁用存活通知
alive_notification_hours = 6

[domains]
# 域名文件路径，多个路径用逗号分隔
domain_files = domains.txt,project/联想.txt
# 是否递归搜索子目录中的域名文件
recursive_search = true
# 域名文件的扩展名，多个扩展名用逗号分隔
domain_file_extensions = .txt, .list, .domains
# 是否加载自定义域名文件
load_custom_domains = false
# 新增域名时是否立即进行子域名收集
scan_new_domains = true
# 首次运行时是否发送通知
notify_on_first_run = false
# 是否启用域名文件动态加载
dynamic_loading = true
# 域名文件检查间隔（分钟）
file_check_interval = 60
# 是否自动移除不在域名文件中的域名
auto_remove_domains = true

[database]
# MySQL配置
mysql_host = ************
mysql_port = 53306
mysql_user = root
mysql_password = g1wXuNKKl3CoZo5z7R
mysql_database = certmonitor
# 连接超时时间（秒）
connect_timeout = 10
# 最大重试次数
max_retries = 3
# 重试延迟时间（秒）
retry_delay = 5
# 连接池大小
pool_size = 5
# 连接池名称
pool_name = certmonitor_pool



[crtsh]
# 是否启用crt.sh搜索
enabled = true
# 搜索间隔（小时）
interval = 24
# 每次查询的超时时间（秒）
timeout = 60
# 每次查询的重试次数
retries = 5
# 每次查询的延迟时间（秒）
delay = 300
# 是否使用缓存
use_cache = true
# 缓存有效期（秒）
cache_ttl = 3600
# 最近子域名的天数阈值（只对最近N天内首次发现的子域名发送通知）
recent_days_threshold = 90

[letsencrypt]
# 是否启用Let's Encrypt CT日志监控
enabled = true
# Let's Encrypt Oak 2025h1日志URL
oak_2025h1_url = https://oak.ct.letsencrypt.org/2025h1/
# Let's Encrypt Oak 2025h2日志URL
oak_2025h2_url = https://oak.ct.letsencrypt.org/2025h2/
# 检查间隔（秒）
check_interval = 180
# 请求超时时间（秒）
timeout = 30

[cloudflare]
# 是否启用Cloudflare CT日志监控
enabled = true
# Cloudflare Nimbus 2025日志URL
nimbus_2025_url = https://ct.cloudflare.com/logs/nimbus2025/
# 检查间隔（秒）
check_interval = 180
# 请求超时时间（秒）
timeout = 30

[export]
# 导出目录
export_dir = exports

[notification]
# 是否启用通知
enabled = true
# 检查未通知变更的间隔（秒）
check_interval = 60
# 批量通知的大小
batch_size = 50
# 批量通知的超时时间（秒）
batch_timeout = 300

[wechat]
# 是否启用微信通知
enabled = true
# 企业微信机器人webhook地址
webhook_url = https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e4fd3203-0828-4545-9632-5f91297d51ff
# 请求超时时间（秒）
timeout = 10
# 请求重试次数
retries = 3
# 重试延迟时间（秒）
delay = 20
# 速率限制（每分钟最大消息数）
rate_limit = 20
