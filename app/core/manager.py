#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
监控管理模块 - 管理所有监控器
"""

from loguru import logger
import threading
from app.utils.config import get_config_instance
from app.db.manager import get_db_instance
from app.core.crtsh_monitor import get_crtsh_instance
from app.core.ctlog_monitor import get_ctlog_instance
from app.notifiers.notifier import get_notifier_instance

class MonitorManager:
    """监控管理类"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """单例模式"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(MonitorManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self, config_file='app/config/config.ini'):
        """初始化监控管理器"""
        # 避免重复初始化
        if self._initialized:
            return

        self.config_file = config_file
        self.config = get_config_instance(config_file)
        self.db = get_db_instance(config_file)
        self.crtsh = get_crtsh_instance(config_file)
        self.ctlog = get_ctlog_instance(config_file)
        self.notifier = get_notifier_instance(config_file)

        # 获取域名配置
        self.scan_new_domains = self.config.getboolean('domains', 'scan_new_domains', fallback=True)
        self.notify_on_first_run = self.config.getboolean('domains', 'notify_on_first_run', fallback=False)
        self.dynamic_loading = self.config.getboolean('domains', 'dynamic_loading', fallback=True)
        self.auto_remove_domains = self.config.getboolean('domains', 'auto_remove_domains', fallback=True)

        # 检查是否是首次运行
        self.is_first_run = self._check_first_run()

        # 注册域名文件变化回调
        if self.dynamic_loading:
            self.config.register_domain_files_callback(self._handle_domain_files_change)

        # 设置CT Log监控回调和监控域名
        self.ctlog.set_callback(self._process_ct_log_message)
        self._update_ctlog_monitored_domains()

        self._initialized = True

    def _update_ctlog_monitored_domains(self):
        """更新CT Log监控的域名列表"""
        try:
            domains = self.db.get_all_domains()
            domain_names = [d['name'] for d in domains] if domains else []
            self.ctlog.set_monitored_domains(domain_names)
            logger.debug(f"更新CT Log监控域名: {len(domain_names)} 个")
        except Exception as e:
            logger.error(f"更新CT Log监控域名失败: {e}")

    def _check_first_run(self):
        """检查是否是首次运行"""
        # 获取所有域名
        domains = self.db.get_all_domains()

        # 如果没有域名，则认为是首次运行
        if not domains:
            logger.info("检测到首次运行，没有找到已监控的域名")
            return True

        # 如果有域名但没有子域名，也认为是首次运行
        for domain in domains:
            if domain['subdomain_count'] > 0:
                return False

        logger.info("检测到首次运行，已有域名但没有子域名")
        return True

    def _handle_domain_files_change(self, changed_files):
        """处理域名文件变化"""
        logger.info(f"检测到域名文件变化: {', '.join(changed_files)}")

        # 加载所有域名
        new_domains = self.config.load_domains_from_files()
        logger.info(f"从域名文件中加载了 {len(new_domains)} 个域名")

        # 获取当前监控的所有域名
        all_domains = self.db.get_all_domains()
        current_domains = {domain['name']: domain['id'] for domain in all_domains}
        logger.info(f"当前监控列表中有 {len(current_domains)} 个域名")

        # 如果没有找到任何域名，并且当前有监控的域名，则可能是所有域名文件都被删除了
        if not new_domains and current_domains and self.auto_remove_domains:
            logger.warning("域名文件变化后未找到要监控的域名，将移除所有当前监控的域名")
            for domain in current_domains:
                logger.info(f"准备移除域名: {domain}")
                self.remove_domain(domain)
            logger.info(f"已移除所有 {len(current_domains)} 个域名")
            return

        # 如果没有找到任何域名，但也没有当前监控的域名，则直接返回
        if not new_domains:
            logger.warning("域名文件变化后未找到要监控的域名")
            return

        # 找出新增的域名
        added_domains = []
        for domain in new_domains:
            if domain not in current_domains:
                added_domains.append(domain)

        # 找出需要删除的域名（不在新域名列表中的旧域名）
        removed_domains = []
        for domain in current_domains:
            if domain not in new_domains:
                removed_domains.append(domain)

        logger.info(f"域名文件变化分析结果: 新增 {len(added_domains)} 个域名, 移除 {len(removed_domains)} 个域名")

        # 记录所有需要删除的域名
        if removed_domains:
            logger.info(f"需要移除的域名: {', '.join(removed_domains)}")

        # 添加新域名
        if added_domains:
            logger.info(f"域名文件变化，新增 {len(added_domains)} 个域名")
            for domain in added_domains:
                self.add_domain(domain, source='file_change')

        # 删除旧域名
        if removed_domains and self.auto_remove_domains:
            logger.info(f"域名文件变化，移除 {len(removed_domains)} 个域名")
            for domain in removed_domains:
                logger.info(f"移除域名: {domain}")
                self.remove_domain(domain)

        logger.info(f"域名文件变化处理完成，新增: {len(added_domains)}, 移除: {len(removed_domains) if self.auto_remove_domains else 0}")

        # 更新CT Log监控域名
        self._update_ctlog_monitored_domains()

    def _process_ct_log_message(self, message):
        """处理CT Log消息（类似CertStream消息格式）"""
        try:
            if message.get('message_type') != 'certificate_update':
                return

            # 获取证书中的域名
            leaf_cert = message.get('data', {}).get('leaf_cert', {})
            all_domains = []

            # 从CN获取域名
            if 'subject' in leaf_cert and 'CN' in leaf_cert['subject']:
                cn = leaf_cert['subject']['CN']
                if cn:
                    all_domains.append(cn.lower())

            # 从SAN获取域名
            if 'extensions' in leaf_cert and 'subject_alt_name' in leaf_cert['extensions']:
                for domain_entry in leaf_cert['extensions']['subject_alt_name']:
                    if domain_entry.startswith('DNS:'):
                        domain = domain_entry[4:].lower()
                        if domain and domain not in all_domains:
                            all_domains.append(domain)

            # 检查每个域名是否是监控域名的子域名
            from app.core.subdomain_processor import get_processor_instance
            processor = get_processor_instance(self.config_file)

            for subdomain in all_domains:
                parent_domain = self._is_domain_monitored(subdomain)
                if parent_domain:
                    # 使用子域名处理器处理子域名
                    source_info = message.get('data', {}).get('source', {})
                    source = f"ct_log_{source_info.get('name', 'unknown')}"
                    processor.process_subdomain(parent_domain, subdomain, source)

        except Exception as e:
            logger.error(f"处理CT Log消息时出错: {str(e)}")

    def _is_domain_monitored(self, domain):
        """检查域名是否被监控"""
        # 获取所有监控的域名
        domains = self.db.get_all_domains()
        domain_names = {d['name'] for d in domains}

        # 首先检查完全匹配
        if domain in domain_names:
            return domain

        # 然后检查是否是监控域名的子域名
        parts = domain.split('.')
        for i in range(1, len(parts)):
            parent_domain = '.'.join(parts[i:])
            if parent_domain in domain_names:
                return parent_domain

        return None

    def start_monitoring(self):
        """启动所有监控器"""
        # 检查是否是首次运行
        if self.is_first_run:
            logger.info("检测到首次运行，将进行初始化扫描")

            # 如果配置为首次运行不发送通知，则临时禁用通知
            original_enabled = self.notifier.enabled
            if not self.notify_on_first_run:
                logger.info("首次运行，暂时禁用通知")
                self.notifier.enabled = False

            try:
                # 启动通知管理器
                self.notifier.start()

                # 对所有域名进行一次初始扫描
                if self.scan_new_domains and self.crtsh.enabled:
                    domains = self.db.get_all_domains()
                    if domains:
                        logger.info(f"开始对 {len(domains)} 个域名进行初始扫描...")

                        # 限制一次性处理的域名数量，避免过多请求
                        max_domains = 20
                        if len(domains) > max_domains:
                            logger.info(f"域名过多，只处理前 {max_domains} 个")
                            domains = domains[:max_domains]

                        for domain in domains:
                            logger.info(f"正在使用crt.sh查询 {domain['name']} 的子域名...")
                            self.crtsh.check_domain(domain['name'])

                # 恢复通知设置
                if not self.notify_on_first_run:
                    self.notifier.enabled = original_enabled
                    logger.info("恢复通知设置")

                # 更新首次运行状态
                self.is_first_run = False

            except Exception as e:
                logger.error(f"初始化扫描出错: {str(e)}")
                # 确保恢复通知设置
                if not self.notify_on_first_run:
                    self.notifier.enabled = original_enabled
        else:
            # 启动通知管理器
            self.notifier.start()

        # 启动crt.sh监控
        self.crtsh.start()

        # 启动CT Log监控
        self.ctlog.start()

        # 启动域名文件监控
        if self.dynamic_loading:
            self.config.start_domain_files_monitor()
            logger.info("域名文件动态加载已启动")

        logger.info("所有监控器已启动")

    def stop_monitoring(self):
        """停止所有监控器"""
        # 停止CT Log监控
        self.ctlog.stop()

        # 停止crt.sh监控
        self.crtsh.stop()

        # 停止通知管理器
        self.notifier.stop()

        logger.info("所有监控器已停止")

    def add_domain(self, domain_name, source=None):
        """添加要监控的域名"""
        # 检查域名是否已存在
        domain_id = self.db.get_domain_id(domain_name)
        if domain_id:
            logger.warning(f"域名 {domain_name} 已在监控列表中")
            return False

        # 添加域名
        domain_id = self.db.add_domain(domain_name, source=source)
        logger.info(f"已添加 {domain_name} 到监控列表")

        # 更新CT Log监控域名
        self._update_ctlog_monitored_domains()

        # 如果配置了新增域名时立即进行子域名收集
        if self.scan_new_domains:
            # 临时禁用通知（如果是首次运行且配置为不发送通知）
            original_enabled = self.notifier.enabled
            if self.is_first_run and not self.notify_on_first_run:
                logger.info(f"首次运行，暂时禁用通知")
                self.notifier.enabled = False

            try:
                # 立即使用crt.sh查询子域名
                if self.crtsh.enabled:
                    logger.info(f"正在使用crt.sh查询 {domain_name} 的子域名...")
                    new_count = self.crtsh.check_domain(domain_name)
                    if new_count > 0:
                        logger.info(f"crt.sh查询发现 {new_count} 个子域名")
                    else:
                        logger.info(f"crt.sh查询未发现子域名")
            finally:
                # 恢复通知设置
                if self.is_first_run and not self.notify_on_first_run:
                    self.notifier.enabled = original_enabled
                    logger.info(f"恢复通知设置")

        return True

    def add_domains_from_files(self):
        """从配置的文件中添加域名"""
        # 加载所有域名
        domains = self.config.load_domains_from_files()
        if not domains:
            logger.warning("未找到要监控的域名")
            return 0

        # 临时禁用通知（如果是首次运行且配置为不发送通知）
        original_enabled = self.notifier.enabled
        if self.is_first_run and not self.notify_on_first_run:
            logger.info(f"首次运行，暂时禁用通知")
            self.notifier.enabled = False

        try:
            # 添加域名
            added_count = 0
            new_domains = []

            for domain in domains:
                domain_id = self.db.get_domain_id(domain)
                if not domain_id:
                    self.db.add_domain(domain, source='file')
                    added_count += 1
                    new_domains.append(domain)

            if added_count > 0:
                logger.info(f"已从文件中添加 {added_count} 个域名到监控列表")

                # 如果配置了新增域名时立即进行子域名收集
                if self.scan_new_domains and self.crtsh.enabled:
                    logger.info(f"开始为新增域名收集子域名...")

                    # 限制一次性处理的域名数量，避免过多请求
                    max_domains = 10
                    if len(new_domains) > max_domains:
                        logger.info(f"新增域名过多，只处理前 {max_domains} 个")
                        new_domains = new_domains[:max_domains]

                    for domain in new_domains:
                        logger.info(f"正在使用crt.sh查询 {domain} 的子域名...")
                        new_count = self.crtsh.check_domain(domain)
                        if new_count > 0:
                            logger.info(f"crt.sh查询发现 {domain} 的 {new_count} 个子域名")
                        else:
                            logger.info(f"crt.sh查询未发现 {domain} 的子域名")
            else:
                logger.info("所有域名已在监控列表中")

        finally:
            # 恢复通知设置
            if self.is_first_run and not self.notify_on_first_run:
                self.notifier.enabled = original_enabled
                logger.info(f"恢复通知设置")

        return added_count

    def remove_domain(self, domain_name):
        """从监控列表中移除域名"""
        # 获取域名ID
        domain_id = self.db.get_domain_id(domain_name)
        if not domain_id:
            logger.warning(f"域名 {domain_name} 不在监控列表中，无法移除")
            return False

        # 获取子域名数量
        subdomains = self.db.get_subdomains(domain_id)
        subdomain_count = len(subdomains) if subdomains else 0

        # 移除域名
        if self.db.remove_domain(domain_name):
            logger.info(f"已从监控列表中移除 {domain_name} (ID: {domain_id}, 子域名数量: {subdomain_count})")
            return True
        else:
            logger.warning(f"移除域名 {domain_name} 失败")
            return False

    def list_domains(self):
        """列出所有监控的域名"""
        # 获取所有域名
        domains = self.db.get_all_domains()
        if not domains:
            logger.warning("监控列表为空")
            return []

        return domains

    def export_subdomains(self, domain_name):
        """导出指定域名的所有子域名"""
        # 获取域名ID
        domain_id = self.db.get_domain_id(domain_name)
        if not domain_id:
            logger.warning(f"域名 {domain_name} 不在监控列表中")
            return None

        # 获取所有子域名
        subdomains = self.db.get_subdomains(domain_id)
        if not subdomains:
            logger.warning(f"域名 {domain_name} 没有记录的子域名")
            return []

        return subdomains

    def get_recent_changes(self, limit=20):
        """获取最近的变更记录"""
        return self.db.get_recent_changes(limit)

# 全局实例
_monitor_manager_instance = None

def get_monitor_manager(config_file='app/config/config.ini'):
    """获取监控管理器实例"""
    global _monitor_manager_instance
    if _monitor_manager_instance is None:
        _monitor_manager_instance = MonitorManager(config_file)
    return _monitor_manager_instance
