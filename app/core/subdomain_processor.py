#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
子域名处理器 - 提供通用的子域名处理逻辑，包括域名验证功能
"""

import time
import socket
import requests
import re
import random
from datetime import datetime
from urllib.parse import urlparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from bs4 import BeautifulSoup
from requests.packages.urllib3.exceptions import InsecureRequestWarning
from loguru import logger

# 禁用不安全请求的警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)


class DomainValidator:
    """域名验证和信息获取类"""

    def __init__(self, timeout=10, max_workers=10, max_retries=2):
        """初始化域名验证器"""
        self.timeout = timeout
        self.max_workers = max_workers
        self.max_retries = max_retries
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
        ]

    def validate_domain(self, domain):
        """验证域名是否有效（可解析）"""
        try:
            # 尝试解析域名
            socket.gethostbyname(domain)
            return True
        except socket.gaierror:
            logger.debug(f"域名无法解析: {domain}")
            return False
        except Exception as e:
            logger.warning(f"验证域名时出错: {domain}, 错误: {str(e)}")
            return False

    def get_domain_info(self, domain):
        """获取域名的网站信息（标题和body大小）"""
        if not self.validate_domain(domain):
            return {
                'valid': False,
                'title': None,
                'body_size': None,
                'status_code': None,
                'error': '域名无法解析'
            }

        # 尝试HTTP和HTTPS
        protocols = ['https', 'http']

        for protocol in protocols:
            url = f"{protocol}://{domain}"
            result = self._fetch_url_info(url)

            # 如果成功获取信息，直接返回
            if result['valid']:
                return result

        # 如果所有尝试都失败
        return {
            'valid': False,
            'title': None,
            'body_size': None,
            'status_code': None,
            'error': '无法连接到网站'
        }

    def _fetch_url_info(self, url):
        """获取URL的信息"""
        for attempt in range(self.max_retries):
            try:
                # 随机选择一个User-Agent
                headers = {
                    'User-Agent': random.choice(self.user_agents),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Connection': 'close'  # 避免保持连接
                }

                # 发送请求
                response = requests.get(
                    url,
                    headers=headers,
                    timeout=self.timeout,
                    verify=False,  # 不验证SSL证书
                    allow_redirects=True  # 允许重定向
                )

                # 获取响应内容
                body = response.text
                body_size = len(response.content)

                # 提取标题
                title = None
                try:
                    soup = BeautifulSoup(body, 'html.parser')
                    title_tag = soup.find('title')
                    if title_tag:
                        title = title_tag.text.strip()
                except Exception as e:
                    logger.debug(f"提取标题时出错: {url}, 错误: {str(e)}")

                return {
                    'valid': True,
                    'title': title,
                    'body_size': body_size,
                    'status_code': response.status_code,
                    'url': response.url  # 可能经过重定向
                }

            except requests.exceptions.Timeout:
                logger.debug(f"请求超时: {url} (尝试 {attempt+1}/{self.max_retries})")
            except requests.exceptions.ConnectionError:
                logger.debug(f"连接错误: {url} (尝试 {attempt+1}/{self.max_retries})")
            except requests.exceptions.RequestException as e:
                logger.debug(f"请求错误: {url}, 错误: {str(e)} (尝试 {attempt+1}/{self.max_retries})")
            except Exception as e:
                logger.debug(f"获取URL信息时出错: {url}, 错误: {str(e)} (尝试 {attempt+1}/{self.max_retries})")

            # 如果不是最后一次尝试，等待一段时间后重试
            if attempt < self.max_retries - 1:
                time.sleep(1)

        # 所有尝试都失败
        return {
            'valid': False,
            'title': None,
            'body_size': None,
            'status_code': None,
            'error': '请求失败'
        }


class SubdomainProcessor:
    """子域名处理器，提供通用的子域名处理逻辑"""

    def __init__(self, config, db, notifier, validator):
        """初始化子域名处理器"""
        self.config = config
        self.db = db
        self.notifier = notifier
        self.validator = validator

    def process_subdomain(self, domain, subdomain, source, first_seen=None, is_recent=True):
        """
        处理子域名（验证、添加到数据库、发送通知）

        参数:
            domain: 父域名
            subdomain: 子域名
            source: 来源（'certstream' 或 'crtsh'）
            first_seen: 首次发现时间（可选）
            is_recent: 是否是最近的子域名（默认为True）

        返回:
            change_id: 变更ID，如果处理失败则返回None
        """
        # 检查子域名是否已存在
        domain_id = self.db.get_domain_id(domain)
        if not domain_id:
            logger.warning(f"域名不存在: {domain}")
            return None

        if self.db.is_subdomain_exists(domain_id, subdomain):
            logger.debug(f"子域名已存在: {subdomain} (域名: {domain})")
            return None

        # 先添加到数据库，不管是否有效
        change_id = self.db.add_subdomain(domain_id, subdomain, source=source, first_seen=first_seen)
        if not change_id:
            logger.warning(f"添加子域名失败: {subdomain} (域名: {domain})")
            return None

        first_seen_info = f", 首次发现: {first_seen}" if first_seen else ""
        recent_info = "" if is_recent else " (非最近)"

        # 只对最近的子域名进行验证和信息获取
        if is_recent:
            # 验证域名是否有效
            domain_info = self.validator.get_domain_info(subdomain)

            if domain_info['valid']:
                # 记录日志
                title = domain_info['title'] or '无标题'
                body_size = domain_info['body_size'] or 0
                status_code = domain_info['status_code'] or 'N/A'

                logger.info(f"发现新有效子域名: {subdomain} (域名: {domain}{first_seen_info})")
                logger.debug(f"域名信息: 标题: {title}, 大小: {body_size}字节, 状态码: {status_code}")

                # 发送通知
                if self.notifier.notify_new_subdomain(domain, subdomain, source, first_seen, domain_info):
                    # 标记为已通知
                    self.db.mark_changes_as_notified([change_id])
                    logger.info(f"已发送通知: {subdomain} (域名: {domain})")
                else:
                    logger.warning(f"发送通知失败: {subdomain} (域名: {domain})")
            else:
                # 域名无效，但仍然保留在数据库中
                logger.debug(f"域名无效，但已添加到数据库: {subdomain} (域名: {domain}, 错误: {domain_info.get('error', '未知错误')})")
                # 标记为已通知（不发送通知）
                self.db.mark_changes_as_notified([change_id])
        else:
            # 对于非最近的子域名，不进行验证和信息获取，直接标记为已通知
            logger.debug(f"发现新子域名（非最近）: {subdomain} (域名: {domain}{first_seen_info})")
            logger.debug(f"跳过验证和通知（非最近子域名）: {subdomain} (域名: {domain})")
            # 标记为已通知（不发送通知）
            self.db.mark_changes_as_notified([change_id])

        return change_id

    def process_subdomains_batch(self, domain, subdomains, source, first_seen_map=None, is_recent_map=None):
        """
        批量处理子域名

        参数:
            domain: 父域名
            subdomains: 子域名列表
            source: 来源（'certstream' 或 'crtsh'）
            first_seen_map: 子域名到首次发现时间的映射（可选）
            is_recent_map: 子域名到是否最近的映射（可选）

        返回:
            processed_count: 成功处理的子域名数量
        """
        if not subdomains:
            return 0

        processed_count = 0

        for subdomain in subdomains:
            first_seen = first_seen_map.get(subdomain) if first_seen_map else None
            is_recent = is_recent_map.get(subdomain, True) if is_recent_map else True

            if self.process_subdomain(domain, subdomain, source, first_seen, is_recent):
                processed_count += 1

        return processed_count


