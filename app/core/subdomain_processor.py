#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
子域名处理器 - 提供通用的子域名处理逻辑
"""

import logging
from configparser import ConfigParser
from app.db.manager import get_db_instance
from app.notifiers.notifier import get_notifier_instance
from app.utils.domain_validator import get_validator_instance

class SubdomainProcessor:
    """子域名处理器，提供通用的子域名处理逻辑"""

    _instance = None

    def __new__(cls, *args, **kwargs):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(SubdomainProcessor, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, config_file='app/config/config.ini'):
        """初始化子域名处理器"""
        # 避免重复初始化
        if self._initialized:
            return

        # 保存配置文件路径
        self.config_file = config_file

        # 读取配置
        self.config = ConfigParser()
        self.config.read(config_file)

        # 获取数据库实例
        self.db = get_db_instance(config_file)

        # 获取通知实例
        self.notifier = get_notifier_instance(config_file)

        # 获取域名验证器实例
        self.validator = get_validator_instance()

        self._initialized = True

    def process_subdomain(self, domain, subdomain, source, first_seen=None, is_recent=True):
        """
        处理子域名（验证、添加到数据库、发送通知）

        参数:
            domain: 父域名
            subdomain: 子域名
            source: 来源（'certstream' 或 'crtsh'）
            first_seen: 首次发现时间（可选）
            is_recent: 是否是最近的子域名（默认为True）

        返回:
            change_id: 变更ID，如果处理失败则返回None
        """
        # 检查子域名是否已存在
        domain_id = self.db.get_domain_id(domain)
        if not domain_id:
            logging.warning(f"域名不存在: {domain}")
            return None

        if self.db.is_subdomain_exists(domain_id, subdomain):
            logging.debug(f"子域名已存在: {subdomain} (域名: {domain})")
            return None

        # 先添加到数据库，不管是否有效
        change_id = self.db.add_subdomain(domain_id, subdomain, source=source, first_seen=first_seen)
        if not change_id:
            logging.warning(f"添加子域名失败: {subdomain} (域名: {domain})")
            return None

        first_seen_info = f", 首次发现: {first_seen}" if first_seen else ""
        recent_info = "" if is_recent else " (非最近)"

        # 只对最近的子域名进行验证和信息获取
        if is_recent:
            # 验证域名是否有效
            domain_info = self.validator.get_domain_info(subdomain)

            if domain_info['valid']:
                # 记录日志
                title = domain_info['title'] or '无标题'
                body_size = domain_info['body_size'] or 0
                status_code = domain_info['status_code'] or 'N/A'

                logging.info(f"发现新有效子域名: {subdomain} (域名: {domain}{first_seen_info})")
                logging.info(f"域名信息: 标题: {title}, 大小: {body_size}字节, 状态码: {status_code}")

                # 发送通知
                if self.notifier.notify_new_subdomain(domain, subdomain, source, first_seen, domain_info):
                    # 标记为已通知
                    self.db.mark_changes_as_notified([change_id])
                    logging.info(f"已发送通知: {subdomain} (域名: {domain})")
                else:
                    logging.warning(f"发送通知失败: {subdomain} (域名: {domain})")
            else:
                # 域名无效，但仍然保留在数据库中
                logging.info(f"域名无效，但已添加到数据库: {subdomain} (域名: {domain}, 错误: {domain_info.get('error', '未知错误')})")
                # 标记为已通知（不发送通知）
                self.db.mark_changes_as_notified([change_id])
        else:
            # 对于非最近的子域名，不进行验证和信息获取，直接标记为已通知
            logging.info(f"发现新子域名（非最近）: {subdomain} (域名: {domain}{first_seen_info})")
            logging.info(f"跳过验证和通知（非最近子域名）: {subdomain} (域名: {domain})")
            # 标记为已通知（不发送通知）
            self.db.mark_changes_as_notified([change_id])

        return change_id

    def process_subdomains_batch(self, domain, subdomains, source, first_seen_map=None, is_recent_map=None):
        """
        批量处理子域名

        参数:
            domain: 父域名
            subdomains: 子域名列表
            source: 来源（'certstream' 或 'crtsh'）
            first_seen_map: 子域名到首次发现时间的映射（可选）
            is_recent_map: 子域名到是否最近的映射（可选）

        返回:
            processed_count: 成功处理的子域名数量
        """
        if not subdomains:
            return 0

        processed_count = 0

        for subdomain in subdomains:
            first_seen = first_seen_map.get(subdomain) if first_seen_map else None
            is_recent = is_recent_map.get(subdomain, True) if is_recent_map else True

            if self.process_subdomain(domain, subdomain, source, first_seen, is_recent):
                processed_count += 1

        return processed_count

# 全局实例
_processor_instance = None

def get_processor_instance(config_file='app/config/config.ini'):
    """获取子域名处理器实例"""
    global _processor_instance
    if _processor_instance is None:
        _processor_instance = SubdomainProcessor(config_file)
    return _processor_instance
