#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
crt.sh监控模块 - 通过查询crt.sh网站获取子域名信息
"""

import time
import json
from loguru import logger
import threading
import requests
from datetime import datetime, timedelta
from configparser import ConfigParser
from app.db.manager import get_db_instance
from app.notifiers.notifier import get_notifier_instance
from app.utils.domain_validator import get_validator_instance
from app.core.subdomain_processor import get_processor_instance

class CrtShMonitor:
    """crt.sh监控类"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """单例模式"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(CrtShMonitor, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self, config_file='app/config/config.ini'):
        """初始化crt.sh监控器"""
        # 避免重复初始化
        if self._initialized:
            return

        self.config_file = config_file
        self.config = ConfigParser()
        self.config.read(config_file)

        # 获取crt.sh配置
        self.enabled = self.config.getboolean('crtsh', 'enabled', fallback=True)
        self.interval = self.config.getfloat('crtsh', 'interval', fallback=24)
        self.timeout = self.config.getint('crtsh', 'timeout', fallback=60)
        self.retries = self.config.getint('crtsh', 'retries', fallback=5)
        self.delay = self.config.getint('crtsh', 'delay', fallback=10)
        self.concurrency = self.config.getint('crtsh', 'concurrency', fallback=5)

        # 缓存配置
        self.use_cache = self.config.getboolean('crtsh', 'use_cache', fallback=True)
        self.cache_ttl = self.config.getint('crtsh', 'cache_ttl', fallback=3600)
        self.cache = {}
        self.cache_expiry = {}

        # 最近子域名的天数阈值
        self.recent_days_threshold = self.config.getint('crtsh', 'recent_days_threshold', fallback=30)

        # 获取数据库实例
        self.db = get_db_instance(config_file)

        # 获取通知实例
        self.notifier = get_notifier_instance(config_file)

        # 获取域名验证器实例
        self.validator = get_validator_instance()

        # 获取子域名处理器实例
        self.processor = get_processor_instance(config_file)

        # 监控线程
        self.running = False
        self.thread = None

        self._initialized = True

    def start(self):
        """启动crt.sh监控"""
        if not self.enabled:
            logger.info("crt.sh监控已禁用")
            return

        if self.running:
            logger.warning("crt.sh监控已在运行")
            return

        # 通知管理器由 MonitorManager 统一启动
        # self.notifier.start()

        # 启动监控线程
        self.running = True
        self.thread = threading.Thread(target=self._monitoring_loop)
        self.thread.daemon = True
        self.thread.start()

        logger.info(f"crt.sh监控已启动，间隔: {self.interval}小时")

    def stop(self):
        """停止crt.sh监控"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1)
            self.thread = None

        logger.info("crt.sh监控已停止")

    def query_crt_sh(self, domain):
        """查询crt.sh网站获取子域名信息"""
        # 检查缓存
        cache_key = f"crtsh_{domain}"
        if self.use_cache and cache_key in self.cache:
            if time.time() < self.cache_expiry.get(cache_key, 0):
                logger.info(f"从缓存获取 {domain} 的证书数据")
                return self.cache[cache_key]

        url = f"https://crt.sh/?q=%.{domain}&output=json"

        # 添加随机延迟，避免请求模式过于规律
        random_delay = self.delay + (time.time() % 10)
        time.sleep(random_delay)

        for attempt in range(self.retries):
            try:
                logger.info(f"正在查询crt.sh: {domain} (尝试 {attempt+1}/{self.retries})")

                # 添加User-Agent头，模拟浏览器访问
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'application/json',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }

                response = requests.get(url, headers=headers, timeout=self.timeout)

                if response.status_code == 200:
                    try:
                        result = response.json()
                        logger.info(f"成功获取 {domain} 的证书数据: {len(result)} 条记录")

                        # 更新缓存
                        if self.use_cache:
                            self.cache[cache_key] = result
                            self.cache_expiry[cache_key] = time.time() + self.cache_ttl

                        return result
                    except json.JSONDecodeError as e:
                        logger.warning(f"解析crt.sh响应JSON失败: {str(e)}")
                        # 可能是临时错误，等待后重试
                        time.sleep(self.delay * 2)
                else:
                    logger.warning(f"查询crt.sh失败: HTTP状态码 {response.status_code}")

                    # 根据不同的状态码采取不同的策略
                    if response.status_code == 429:  # Too Many Requests
                        # 请求过多，等待更长时间
                        wait_time = self.delay * 5
                        logger.warning(f"请求过多，等待 {wait_time} 秒后重试")
                        time.sleep(wait_time)
                    elif response.status_code >= 500:  # 服务器错误
                        # 服务器错误，等待较长时间
                        wait_time = self.delay * 3
                        logger.warning(f"服务器错误，等待 {wait_time} 秒后重试")
                        time.sleep(wait_time)
                    else:  # 其他错误
                        time.sleep(self.delay)

            except requests.exceptions.Timeout:
                logger.warning(f"查询crt.sh超时")
                time.sleep(self.delay * 2)

            except requests.exceptions.ConnectionError:
                logger.warning(f"连接crt.sh失败")
                time.sleep(self.delay * 3)

            except Exception as e:
                logger.warning(f"查询crt.sh出错: {str(e)}")
                logger.warning(f"错误类型: {type(e).__name__}")
                time.sleep(self.delay * 2)

            # 如果不是最后一次尝试，等待一段时间后重试
            if attempt < self.retries - 1:
                retry_delay = self.delay * (attempt + 1)  # 递增延迟
                logger.info(f"等待 {retry_delay} 秒后进行第 {attempt+2} 次尝试...")
                time.sleep(retry_delay)

        logger.error(f"查询crt.sh失败，已达到最大重试次数: {self.retries}")
        return []

    def extract_subdomains(self, cert_data, domain):
        """从证书数据中提取子域名及其最早日期"""
        # 使用字典存储子域名及其最早日期
        subdomain_dates = {}

        for cert in cert_data:
            # 提取日期信息
            not_before = None
            if 'not_before' in cert:
                try:
                    # 尝试解析日期，格式可能是 "2020-08-10T00:00:00"
                    not_before = cert['not_before'].split('T')[0]  # 只取日期部分
                except Exception as e:
                    logger.debug(f"解析证书日期失败: {str(e)}")

            # 提取common_name字段中的子域名
            if 'common_name' in cert and cert['common_name'].endswith(domain):
                subdomain = cert['common_name'].lower()
                if subdomain not in subdomain_dates or (not_before and (not subdomain_dates[subdomain] or not_before < subdomain_dates[subdomain])):
                    subdomain_dates[subdomain] = not_before

            # 提取name_value字段中的子域名
            if 'name_value' in cert:
                for name in cert['name_value'].split('\n'):
                    if name.endswith(domain):
                        subdomain = name.lower()
                        if subdomain not in subdomain_dates or (not_before and (not subdomain_dates[subdomain] or not_before < subdomain_dates[subdomain])):
                            subdomain_dates[subdomain] = not_before

        # 转换为列表，每个元素是 (subdomain, first_seen) 元组
        result = [(subdomain, date) for subdomain, date in subdomain_dates.items()]
        # 按子域名排序
        result.sort(key=lambda x: x[0])

        return result

    def check_domain(self, domain):
        """检查单个域名的子域名"""
        logger.debug(f"正在查询crt.sh获取 {domain} 的子域名...")

        # 获取域名ID
        domain_id = self.db.get_domain_id(domain)
        if not domain_id:
            logger.warning(f"域名 {domain} 不在监控列表中")
            return 0

        # 查询crt.sh
        cert_data = self.query_crt_sh(domain)
        if not cert_data:
            logger.warning(f"未找到 {domain} 的证书数据")
            return 0

        # 提取子域名及其最早日期
        subdomain_data = self.extract_subdomains(cert_data, domain)
        if not subdomain_data:
            logger.info(f"未找到 {domain} 的子域名")
            return 0

        # 检查新增子域名
        new_count = 0
        new_subdomains = []

        # 获取当前日期，用于筛选最近的子域名
        from datetime import datetime, timedelta
        current_date = datetime.now().date()
        # 使用配置的天数阈值，只关注最近N天内首次发现的子域名
        recent_threshold = current_date - timedelta(days=self.recent_days_threshold)
        logger.debug(f"使用最近子域名阈值: {self.recent_days_threshold}天 (日期: {recent_threshold})")

        for subdomain, first_seen in subdomain_data:
            # 判断是否是最近的子域名
            is_recent = True
            if first_seen:
                try:
                    # 解析日期字符串为日期对象
                    first_seen_date = datetime.strptime(first_seen, "%Y-%m-%d").date()
                    is_recent = first_seen_date >= recent_threshold
                except (ValueError, TypeError):
                    # 如果日期解析失败，默认为最近的
                    logger.warning(f"解析日期失败: {first_seen}，默认为最近的子域名")

            # 使用子域名处理器处理子域名
            change_id = self.processor.process_subdomain(domain, subdomain, 'crtsh', first_seen, is_recent)

            if change_id:
                new_count += 1

        if new_count > 0:
            logger.info(f"crt.sh查询发现 {domain} 的 {new_count} 个新增子域名")
        else:
            logger.info(f"crt.sh查询未发现 {domain} 的新增子域名")

        return new_count

    def check_all_domains(self):
        """检查所有监控域名的子域名"""
        # 获取所有域名
        domains = self.db.get_all_domains()
        if not domains:
            logger.warning("监控列表为空")
            return 0

        total_new = 0

        # 使用单线程顺序查询，避免对crt.sh服务器造成过大压力
        for domain in domains:
            try:
                logger.info(f"开始查询域名: {domain['name']}")
                new_count = self.check_domain(domain['name'])
                total_new += new_count

                # 每次查询后添加延迟，避免请求过于频繁
                time.sleep(self.delay)
            except Exception as e:
                logger.error(f"处理域名 {domain['name']} 时出错: {str(e)}")

        logger.info(f"crt.sh查询完成，共发现 {total_new} 个新增子域名")
        return total_new

    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 执行一次检查
                self.check_all_domains()

                # 计算下次检查时间
                next_check = datetime.now() + timedelta(hours=self.interval)
                logger.info(f"下次crt.sh查询时间: {next_check.strftime('%Y-%m-%d %H:%M:%S')}")

                # 等待到下次检查时间
                while self.running and datetime.now() < next_check:
                    time.sleep(60)  # 每分钟检查一次是否应该退出

            except Exception as e:
                logger.error(f"crt.sh监控循环出错: {str(e)}")
                # 出错后等待一段时间再重试
                time.sleep(300)  # 5分钟

# 全局实例
_crtsh_instance = None

def get_crtsh_instance(config_file='app/config/config.ini'):
    """获取crt.sh监控实例"""
    global _crtsh_instance
    if _crtsh_instance is None:
        _crtsh_instance = CrtShMonitor(config_file)
    return _crtsh_instance
