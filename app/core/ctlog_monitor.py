#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CT Log监控模块 - 统一封装Let's Encrypt和Cloudflare
"""

import time
import json
import requests
import threading
import base64
import struct
from datetime import datetime
from configparser import ConfigParser
from loguru import logger

class CTLogMonitor:
    """CT Log监控类 - 统一管理Let's Encrypt和Cloudflare CT日志"""

    def __init__(self, config_file='app/config/config.ini'):
        """初始化CT Log监控器"""
        self.config_file = config_file
        self.config = ConfigParser()
        self.config.read(config_file)

        # CT日志配置
        self.ct_logs = []
        self._load_ct_logs_config()

        # 运行状态
        self.running = False
        self.threads = []

        # 回调函数
        self.callback = None

        # 最后检查的位置
        self.last_positions = {}

        # 监控的域名列表
        self.monitored_domains = set()

        # 统计信息
        self.stats = {
            'total_certificates': 0,
            'matched_certificates': 0,
            'total_domains': 0,
            'matched_domains': 0,
            'parse_errors': 0,
            'short_data_errors': 0,
            'format_errors': 0,
            'start_time': time.time()
        }

    def _load_ct_logs_config(self):
        """加载CT日志配置"""
        # Let's Encrypt配置
        le_enabled = self.config.getboolean('letsencrypt', 'enabled', fallback=True)
        if le_enabled:
            oak_2025h1_url = self.config.get('letsencrypt', 'oak_2025h1_url', fallback='')
            oak_2025h2_url = self.config.get('letsencrypt', 'oak_2025h2_url', fallback='')
            le_check_interval = self.config.getint('letsencrypt', 'check_interval', fallback=180)
            le_timeout = self.config.getint('letsencrypt', 'timeout', fallback=30)

            if oak_2025h1_url:
                self.ct_logs.append({
                    'name': 'Let\'s Encrypt Oak 2025h1',
                    'url': oak_2025h1_url,
                    'provider': 'letsencrypt',
                    'check_interval': le_check_interval,
                    'timeout': le_timeout,
                    'enabled': True
                })

            if oak_2025h2_url:
                self.ct_logs.append({
                    'name': 'Let\'s Encrypt Oak 2025h2',
                    'url': oak_2025h2_url,
                    'provider': 'letsencrypt',
                    'check_interval': le_check_interval,
                    'timeout': le_timeout,
                    'enabled': True
                })

        # Cloudflare配置
        cf_enabled = self.config.getboolean('cloudflare', 'enabled', fallback=True)
        if cf_enabled:
            nimbus_2025_url = self.config.get('cloudflare', 'nimbus_2025_url', fallback='')
            cf_check_interval = self.config.getint('cloudflare', 'check_interval', fallback=180)
            cf_timeout = self.config.getint('cloudflare', 'timeout', fallback=30)

            if nimbus_2025_url:
                self.ct_logs.append({
                    'name': 'Cloudflare Nimbus 2025',
                    'url': nimbus_2025_url,
                    'provider': 'cloudflare',
                    'check_interval': cf_check_interval,
                    'timeout': cf_timeout,
                    'enabled': True
                })

        logger.info(f"加载了 {len(self.ct_logs)} 个CT日志配置")

    def set_callback(self, callback_function):
        """设置回调函数"""
        self.callback = callback_function

    def set_monitored_domains(self, domains):
        """设置要监控的域名列表"""
        self.monitored_domains = set(domain.lower() for domain in domains)
        logger.info(f"设置监控域名: {len(self.monitored_domains)} 个")

    def start(self):
        """启动CT Log监控"""
        if not self.ct_logs:
            logger.warning("没有配置可用的CT日志")
            return

        if self.running:
            logger.warning("CT Log监控已在运行")
            return

        self.running = True
        self.stats['start_time'] = time.time()

        # 为每个CT日志启动监控线程
        for ct_log in self.ct_logs:
            if ct_log['enabled']:
                thread = threading.Thread(
                    target=self._monitor_ct_log,
                    args=(ct_log,),
                    daemon=True
                )
                thread.start()
                self.threads.append(thread)

        logger.info(f"CT Log监控已启动，监控 {len([log for log in self.ct_logs if log['enabled']])} 个日志")

    def stop(self):
        """停止CT Log监控"""
        if not self.running:
            return

        self.running = False

        # 等待所有线程结束
        for thread in self.threads:
            thread.join(timeout=1)

        self.threads.clear()
        logger.info("CT Log监控已停止")

    def get_stats(self):
        """获取统计信息"""
        elapsed = time.time() - self.stats['start_time']
        return {
            **self.stats,
            'elapsed_time': elapsed,
            'certificate_rate': self.stats['total_certificates'] / elapsed * 60 if elapsed > 0 else 0,
            'domain_rate': self.stats['total_domains'] / elapsed * 60 if elapsed > 0 else 0
        }

    def _monitor_ct_log(self, ct_log):
        """监控单个CT日志 - 持续监听直到发现匹配域名"""
        log_name = ct_log['name']
        log_url = ct_log['url']
        check_interval = ct_log['check_interval']

        logger.info(f"开始持续监控CT日志: {log_name}")

        # 获取初始位置
        if log_name not in self.last_positions:
            try:
                sth = self._get_sth(log_url, ct_log['timeout'])
                if sth:
                    # 从最新位置开始，避免处理历史数据（从最新的10个条目开始）
                    self.last_positions[log_name] = max(0, sth['tree_size'] - 10)
                    logger.info(f"{log_name}: 从位置 {self.last_positions[log_name]} 开始监控")
                else:
                    logger.error(f"无法获取 {log_name} 的初始状态")
                    return
            except Exception as e:
                logger.error(f"初始化 {log_name} 失败: {e}")
                return

        # 持续监听循环
        while self.running:
            try:
                matched_count = self._check_new_entries(ct_log)

                # 如果没有找到匹配的域名，等待一段时间再检查
                if matched_count == 0:
                    time.sleep(check_interval)
                else:
                    # 找到匹配域名后，稍微缩短检查间隔
                    time.sleep(min(30, check_interval // 2))

            except Exception as e:
                logger.error(f"监控 {log_name} 时出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试

    def _get_sth(self, log_url, timeout):
        """获取Signed Tree Head"""
        try:
            response = requests.get(f"{log_url}ct/v1/get-sth", timeout=timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.debug(f"获取STH失败: {e}")
            return None

    def _check_new_entries(self, ct_log):
        """检查新的证书条目，返回匹配的域名数量 - 持续处理所有新条目"""
        log_name = ct_log['name']
        log_url = ct_log['url']
        timeout = ct_log['timeout']

        # 获取当前树大小
        sth = self._get_sth(log_url, timeout)
        if not sth:
            return 0

        current_size = sth['tree_size']
        last_position = self.last_positions.get(log_name, 0)

        if current_size <= last_position:
            return 0  # 没有新条目

        # 处理所有新条目，不设限制，但限制单次处理数量避免过载
        available_entries = current_size - last_position
        max_process_per_round = min(available_entries, 50)  # 单次最多处理50个条目

        logger.debug(f"{log_name}: 处理 {max_process_per_round} 个新条目，从 {last_position} 开始 (总大小: {current_size})")

        matched_count = 0
        processed_count = 0
        error_count = 0

        # 逐个获取和处理条目
        for i in range(max_process_per_round):
            current_index = last_position + i

            # 确保不超过树的边界
            if current_index >= current_size:
                break

            try:
                # 逐个获取单个条目
                response = requests.get(
                    f"{log_url}ct/v1/get-entries",
                    params={'start': current_index, 'end': current_index},
                    timeout=timeout
                )
                response.raise_for_status()
                data = response.json()

                entries = data.get('entries', [])

                # 验证返回了正确的条目
                if len(entries) != 1:
                    logger.debug(f"{log_name}: 条目 #{current_index} 获取失败，期望1个条目，收到{len(entries)}个")
                    error_count += 1
                    # 更新位置，跳过这个有问题的条目
                    self.last_positions[log_name] = current_index + 1
                    continue

                entry = entries[0]

                # 验证条目完整性
                if not entry or 'leaf_input' not in entry:
                    logger.debug(f"{log_name}: 条目 #{current_index} 数据不完整")
                    error_count += 1
                    # 更新位置，跳过这个有问题的条目
                    self.last_positions[log_name] = current_index + 1
                    continue

                # 处理条目
                if self._process_entry(entry, current_index, ct_log):
                    matched_count += 1
                processed_count += 1

                # 更新位置到当前处理的条目的下一个
                self.last_positions[log_name] = current_index + 1

            except requests.exceptions.RequestException as e:
                logger.debug(f"{log_name}: 获取条目 #{current_index} 网络错误: {e}")
                error_count += 1
                # 网络错误时跳过这个条目，继续处理下一个
                self.last_positions[log_name] = current_index + 1
                continue
            except Exception as e:
                logger.debug(f"{log_name}: 处理条目 #{current_index} 失败: {e}")
                error_count += 1
                # 其他错误时也跳过这个条目
                self.last_positions[log_name] = current_index + 1
                continue

        if processed_count > 0 or error_count > 0:
            logger.debug(f"{log_name}: 处理了 {processed_count} 个条目，匹配 {matched_count} 个，跳过错误 {error_count} 个")

        return matched_count

    def _process_entry(self, entry, index, ct_log):
        """处理单个证书条目，返回是否匹配监控域名"""
        try:
            # 解析证书数据
            from cryptography import x509
            from cryptography.hazmat.backends import default_backend

            # 解码leaf_input
            leaf_input = base64.b64decode(entry['leaf_input'])

            # 验证leaf_input长度
            if len(leaf_input) < 15:  # 最小长度检查
                logger.debug(f"条目 #{index}: leaf_input太短 ({len(leaf_input)} 字节)")
                self.stats['parse_errors'] += 1
                return False

            # CT Log leaf格式：
            # version(1) + leaf_type(1) + timestamp(8) + entry_type(2) + cert_length(3) + cert_data
            # 正确的偏移量应该是：1 + 1 + 8 + 2 = 12 bytes
            cert_length_start = 12

            # 安全地提取证书长度
            if len(leaf_input) < cert_length_start + 3:
                logger.debug(f"条目 #{index}: 无法读取证书长度")
                self.stats['parse_errors'] += 1
                return False

            # 读取3字节的证书长度（24位大端序）
            cert_length_bytes = leaf_input[cert_length_start:cert_length_start+3]
            cert_length = struct.unpack('>I', b'\x00' + cert_length_bytes)[0]

            # 验证证书长度合理性
            if cert_length <= 0 or cert_length > 10 * 1024 * 1024:  # 最大10MB
                logger.debug(f"条目 #{index}: 证书长度异常 ({cert_length} 字节)")
                self.stats['parse_errors'] += 1
                return False

            # 证书数据开始位置
            cert_data_start = cert_length_start + 3  # 12 + 3 = 15
            cert_data_end = cert_data_start + cert_length

            # 验证是否有足够的数据
            if len(leaf_input) < cert_data_end:
                logger.debug(f"条目 #{index}: 证书数据不完整 (需要{cert_data_end}字节，只有{len(leaf_input)}字节)")
                self.stats['short_data_errors'] += 1
                return False

            cert_data = leaf_input[cert_data_start:cert_data_end]

            # 验证证书数据不为空
            if not cert_data:
                logger.debug(f"条目 #{index}: 证书数据为空")
                self.stats['parse_errors'] += 1
                return False

            # 解析证书
            try:
                cert = x509.load_der_x509_certificate(cert_data, default_backend())
            except Exception as cert_error:
                # 记录具体的证书解析错误，这些通常是CT日志服务器的数据问题
                error_msg = str(cert_error)
                if "ShortData" in error_msg:
                    logger.debug(f"条目 #{index}: 证书数据不完整 - {error_msg} - 跳过")
                    self.stats['short_data_errors'] += 1
                elif "ParseError" in error_msg:
                    logger.debug(f"条目 #{index}: 证书格式错误 - {error_msg} - 跳过")
                    self.stats['format_errors'] += 1
                else:
                    logger.debug(f"条目 #{index}: 证书解析失败 - {error_msg} - 跳过")
                    self.stats['parse_errors'] += 1
                return False

            # 提取域名
            domains = []

            # 从Subject CN获取
            try:
                cn = cert.subject.get_attributes_for_oid(x509.NameOID.COMMON_NAME)[0].value
                if cn:
                    domains.append(cn.lower())
            except:
                pass

            # 从SAN获取
            try:
                san_ext = cert.extensions.get_extension_for_oid(x509.ExtensionOID.SUBJECT_ALTERNATIVE_NAME)
                for name in san_ext.value:
                    if isinstance(name, x509.DNSName):
                        domains.append(name.value.lower())
            except:
                pass

            # 更新统计信息
            self.stats['total_certificates'] += 1
            self.stats['total_domains'] += len(domains)

            # Debug级别日志：显示接收到的域名信息
            if domains:
                domain_str = ', '.join(domains[:3])
                if len(domains) > 3:
                    domain_str += f" (共{len(domains)}个)"
                logger.debug(f"[{ct_log['provider'].upper()}] 接收到证书 #{index}: {domain_str}")

            # 检查是否匹配监控域名
            matched_domains = []
            for domain in domains:
                if self._is_domain_monitored(domain):
                    matched_domains.append(domain)

            # 如果有匹配的域名且有回调函数，调用它
            if matched_domains and self.callback:
                self.stats['matched_certificates'] += 1
                self.stats['matched_domains'] += len(matched_domains)

                # 构造消息格式
                message = {
                    'message_type': 'certificate_update',
                    'data': {
                        'leaf_cert': {
                            'subject': {'CN': domains[0] if domains else ''},
                            'extensions': {
                                'subject_alt_name': [f'DNS:{domain}' for domain in domains]
                            }
                        },
                        'source': {
                            'name': ct_log['name'],
                            'provider': ct_log['provider'],
                            'url': f"CT Log {ct_log['name']}",
                            'index': index
                        }
                    }
                }

                self.callback(message)

                # 记录匹配信息
                matched_str = ', '.join(matched_domains[:3])
                if len(matched_domains) > 3:
                    matched_str += f" (共{len(matched_domains)}个)"
                logger.info(f"[{ct_log['provider'].upper()}] 发现匹配域名: {matched_str}")

                return True

        except Exception as e:
            logger.debug(f"解析证书条目失败: {e}")

        return False

    def _is_domain_monitored(self, domain):
        """检查域名是否被监控"""
        # 首先检查完全匹配
        if domain in self.monitored_domains:
            return True

        # 然后检查是否是监控域名的子域名
        parts = domain.split('.')
        for i in range(1, len(parts)):
            parent_domain = '.'.join(parts[i:])
            if parent_domain in self.monitored_domains:
                return True

        return False

# 全局实例
_ctlog_instance = None

def get_ctlog_instance(config_file='app/config/config.ini'):
    """获取CT Log监控实例"""
    global _ctlog_instance
    if _ctlog_instance is None:
        _ctlog_instance = CTLogMonitor(config_file)
    return _ctlog_instance
