#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通知模块 - 使用企业微信机器人发送通知
"""

import json
from loguru import logger
import requests
import threading
import time
from datetime import datetime
from configparser import ConfigParser
from app.db.manager import get_db_instance

class Notifier:
    """通知类"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """单例模式"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(Notifier, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self, config_file='app/config/config.ini'):
        """初始化通知器"""
        # 避免重复初始化
        if self._initialized:
            return

        self.config = ConfigParser()
        self.config.read(config_file)

        # 获取通知配置
        self.enabled = self.config.getboolean('notification', 'enabled', fallback=True)
        self.check_interval = self.config.getint('notification', 'check_interval', fallback=60)
        self.batch_size = self.config.getint('notification', 'batch_size', fallback=10)
        self.batch_timeout = self.config.getint('notification', 'batch_timeout', fallback=300)

        # 获取微信配置
        self.wechat_enabled = self.config.getboolean('wechat', 'enabled', fallback=False)
        self.webhook_url = self.config.get('wechat', 'webhook_url', fallback='')
        self.timeout = self.config.getint('wechat', 'timeout', fallback=10)
        self.retries = self.config.getint('wechat', 'retries', fallback=3)
        self.delay = self.config.getint('wechat', 'delay', fallback=2)

        # 速率限制配置
        self.rate_limit = self.config.getint('wechat', 'rate_limit', fallback=20)  # 每分钟最大消息数
        self.rate_limit_period = 60  # 速率限制周期（秒）

        # 速率限制状态
        self.message_timestamps = []  # 记录消息发送时间戳
        self.rate_limit_lock = threading.Lock()  # 用于线程安全的锁

        if self.wechat_enabled and not self.webhook_url:
            logger.warning("微信通知已启用，但未配置webhook_url")
            self.wechat_enabled = False

        # 获取数据库实例
        self.db = get_db_instance(config_file)

        # 通知线程
        self.running = False
        self.thread = None

        # 批量通知缓存
        self.batch_changes = []
        self.last_batch_time = time.time()

        self._initialized = True

    def start(self):
        """启动通知器"""
        if not self.enabled:
            logger.debug("通知功能已禁用")
            return

        if self.running:
            logger.warning("通知器已在运行")
            return

        self.running = True
        self.thread = threading.Thread(target=self._notification_loop)
        self.thread.daemon = True
        self.thread.start()

        logger.info("通知器已启动")

    def stop(self):
        """停止通知器"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1)
            self.thread = None

        logger.info("通知器已停止")

    def _notification_loop(self):
        """通知循环"""
        while self.running:
            try:
                # 检查未通知的变更
                self._check_unnotified_changes()

                # 等待下一次检查
                time.sleep(self.check_interval)

            except Exception as e:
                logger.error(f"通知循环出错: {str(e)}")
                # 出错后等待一段时间再重试
                time.sleep(60)

    def _check_unnotified_changes(self):
        """检查未通知的变更"""
        # 获取未通知的变更
        changes = self.db.get_unnotified_changes()
        if not changes:
            return

        # 添加到批量通知缓存
        self.batch_changes.extend(changes)

        # 检查是否应该发送批量通知
        current_time = time.time()
        should_notify = (
            len(self.batch_changes) >= self.batch_size or
            current_time - self.last_batch_time >= self.batch_timeout
        )

        if should_notify and self.batch_changes:
            # 发送批量通知
            success = self.notify_new_subdomains_batch(self.batch_changes)

            if success:
                # 标记为已通知
                change_ids = [change['id'] for change in self.batch_changes]
                self.db.mark_changes_as_notified(change_ids)

                # 清空批量通知缓存
                self.batch_changes = []
                self.last_batch_time = current_time

    def send_text(self, content):
        """发送文本消息"""
        if not self.enabled or not self.wechat_enabled:
            logger.debug("微信通知未启用")
            return False

        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }

        return self._send_request(data)

    def send_markdown(self, content):
        """发送markdown消息"""
        if not self.enabled or not self.wechat_enabled:
            logger.debug("微信通知未启用")
            return False

        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }

        return self._send_request(data)

    def _check_rate_limit(self):
        """检查是否超过速率限制，如果超过则等待"""
        with self.rate_limit_lock:
            current_time = time.time()

            # 清理过期的时间戳
            self.message_timestamps = [ts for ts in self.message_timestamps
                                      if current_time - ts < self.rate_limit_period]

            # 检查是否超过速率限制
            if len(self.message_timestamps) >= self.rate_limit:
                # 计算需要等待的时间
                oldest_timestamp = min(self.message_timestamps)
                wait_time = self.rate_limit_period - (current_time - oldest_timestamp) + 1

                if wait_time > 0:
                    logger.warning(f"达到速率限制，等待 {wait_time:.1f} 秒后继续发送")
                    return wait_time

            return 0

    def _send_request(self, data):
        """发送请求，包含速率限制"""
        headers = {'Content-Type': 'application/json'}

        # 检查速率限制
        wait_time = self._check_rate_limit()
        if wait_time > 0:
            time.sleep(wait_time)

        for attempt in range(self.retries):
            try:
                response = requests.post(
                    self.webhook_url,
                    headers=headers,
                    data=json.dumps(data),
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('errcode') == 0:
                        # 记录发送时间戳
                        with self.rate_limit_lock:
                            self.message_timestamps.append(time.time())

                        logger.info("微信通知发送成功")
                        return True
                    elif result.get('errcode') == 45009:  # 接口调用超过限制
                        logger.warning("微信通知发送失败: 接口调用超过限制，等待后重试")
                        time.sleep(self.delay * 5)  # 等待更长时间
                    else:
                        logger.error(f"微信通知发送失败: {result.get('errmsg')}")
                elif response.status_code == 429:  # Too Many Requests
                    logger.warning("微信通知发送失败: 请求过多，等待后重试")
                    time.sleep(self.delay * 5)  # 等待更长时间
                else:
                    logger.error(f"微信通知发送失败: HTTP状态码 {response.status_code}")

            except Exception as e:
                logger.error(f"微信通知发送出错: {str(e)}")

            # 如果不是最后一次尝试，等待一段时间后重试
            if attempt < self.retries - 1:
                retry_delay = self.delay * (attempt + 1)  # 递增延迟
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)

        return False

    def _build_notification_content(self, domain, subdomain, source, first_seen=None, domain_info=None):
        """构建通知内容"""
        content = f"### 发现新子域名\n\n" \
                 f"**域名**: {domain}\n\n" \
                 f"**子域名**: {subdomain}\n\n" \
                 f"**来源**: {source}\n\n"

        # 添加首次发现时间（如果有）
        if first_seen:
            content += f"**首次发现**: {first_seen}\n\n"

        # 添加域名信息（如果有）
        if domain_info and domain_info.get('valid'):
            title = domain_info.get('title') or '无标题'
            body_size = domain_info.get('body_size') or 0
            status_code = domain_info.get('status_code') or 'N/A'
            url = domain_info.get('url') or f"https://{subdomain}"

            # 格式化body大小
            if body_size >= 1024 * 1024:
                size_str = f"{body_size / (1024 * 1024):.2f} MB"
            elif body_size >= 1024:
                size_str = f"{body_size / 1024:.2f} KB"
            else:
                size_str = f"{body_size} 字节"

            content += f"**标题**: {title}\n\n" \
                      f"**响应大小**: {size_str}\n\n" \
                      f"**状态码**: {status_code}\n\n" \
                      f"**URL**: {url}\n\n"

        content += f"**发现时间**: {self._get_current_time()}"

        return content

    def notify_new_subdomain(self, domain, subdomain, source, first_seen=None, domain_info=None):
        """通知新发现的子域名"""
        if not self.enabled or not self.wechat_enabled:
            return False

        # 构建通知内容
        content = self._build_notification_content(domain, subdomain, source, first_seen, domain_info)

        # 检查速率限制
        wait_time = self._check_rate_limit()
        if wait_time > 0:
            time.sleep(wait_time)

        # 发送通知并添加重试机制
        for attempt in range(3):  # 最多尝试3次
            if self.send_markdown(content):
                return True

            # 如果发送失败，等待一段时间后重试
            if attempt < 2:  # 如果不是最后一次尝试
                retry_delay = 2 * (attempt + 1)  # 递增延迟
                logger.warning(f"发送通知失败，等待 {retry_delay} 秒后重试 (尝试 {attempt+1}/3)")
                time.sleep(retry_delay)

        return False

    def notify_new_subdomains_batch(self, changes):
        """批量通知新发现的子域名，确保每个域名单独发送"""
        if not self.enabled or not self.wechat_enabled or not changes:
            return False

        # 按域名分组
        domains = {}
        for change in changes:
            domain = change['domain']
            if domain not in domains:
                domains[domain] = []
            domains[domain].append(change)

        # 为每个域名单独发送通知
        success = True
        for domain, domain_changes in domains.items():
            # 分批发送，每批最多10个子域名，避免超过微信消息大小限制
            batches = [domain_changes[i:i+10] for i in range(0, len(domain_changes), 10)]

            for batch_index, batch in enumerate(batches):
                # 构建批次内容
                batch_content = f"### 发现新子域名 - {domain}\n\n"

                if len(batches) > 1:
                    batch_content += f"**批次 {batch_index+1}/{len(batches)}**\n\n"

                for i, change in enumerate(batch, 1):
                    # 添加首次发现时间（如果有）
                    first_seen_info = f", 首次发现: {change.get('first_seen')}" if 'first_seen' in change and change['first_seen'] else ""

                    # 添加域名信息（如果有）
                    domain_info_str = ""
                    if 'domain_info' in change and change['domain_info'] and change['domain_info'].get('valid'):
                        title = change['domain_info'].get('title')
                        if title:
                            domain_info_str = f", 标题: {title}"

                    batch_content += f"{i}. {change['subdomain']} ({change['source']}{first_seen_info}{domain_info_str})\n"

                batch_content += f"\n> **发现时间**: {self._get_current_time()}"

                # 发送这一批次的通知
                if not self.send_markdown(batch_content):
                    logger.error(f"发送 {domain} 的子域名通知失败 (批次 {batch_index+1}/{len(batches)})")
                    success = False
                else:
                    logger.info(f"成功发送 {domain} 的子域名通知 (批次 {batch_index+1}/{len(batches)})")

                # 检查速率限制，如果需要等待则等待
                wait_time = self._check_rate_limit()
                if wait_time > 0:
                    time.sleep(wait_time)
                else:
                    # 添加固定延迟，避免发送过快
                    time.sleep(3)

        return success

    def _get_current_time(self):
        """获取当前时间"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 全局实例
_notifier_instance = None

def get_notifier_instance(config_file='app/config/config.ini'):
    """获取通知器实例"""
    global _notifier_instance
    if _notifier_instance is None:
        _notifier_instance = Notifier(config_file)
    return _notifier_instance
