#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文件监控模块 - 监控文件变化并触发回调函数
"""

import os
import time
import logging
import threading
from datetime import datetime

class FileWatcher:
    """文件监控类，用于监控文件变化并触发回调函数"""

    def __init__(self, file_paths, callback, check_interval=60):
        """
        初始化文件监控器

        参数:
            file_paths: 要监控的文件路径列表或单个文件路径
            callback: 文件变化时触发的回调函数，接收变化的文件路径列表作为参数
            check_interval: 检查间隔（秒）
        """
        self.file_paths = [file_paths] if isinstance(file_paths, str) else file_paths
        self.callback = callback
        self.check_interval = check_interval
        self.file_stats = {}
        self.running = False
        self.thread = None
        self.lock = threading.Lock()

        # 初始化文件状态
        self._init_file_stats()

    def _init_file_stats(self):
        """初始化文件状态"""
        for file_path in self.file_paths:
            try:
                if os.path.exists(file_path):
                    stat = os.stat(file_path)
                    self.file_stats[file_path] = {
                        'mtime': stat.st_mtime,
                        'size': stat.st_size,
                        'exists': True,
                        'last_checked': datetime.now()
                    }
                else:
                    self.file_stats[file_path] = {
                        'mtime': 0,
                        'size': 0,
                        'exists': False,
                        'last_checked': datetime.now()
                    }
            except Exception as e:
                logging.error(f"初始化文件状态失败: {file_path}, 错误: {str(e)}")
                self.file_stats[file_path] = {
                    'mtime': 0,
                    'size': 0,
                    'exists': False,
                    'last_checked': datetime.now(),
                    'error': str(e)
                }

    def start(self):
        """启动文件监控"""
        with self.lock:
            if self.running:
                logging.warning("文件监控器已在运行")
                return

            self.running = True
            self.thread = threading.Thread(target=self._monitoring_loop)
            self.thread.daemon = True
            self.thread.start()

            logging.info(f"文件监控器已启动，监控文件: {', '.join(self.file_paths)}")

    def stop(self):
        """停止文件监控"""
        with self.lock:
            if not self.running:
                return

            self.running = False
            if self.thread:
                self.thread.join(timeout=1)
                self.thread = None

            logging.info("文件监控器已停止")

    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                changed_files = self._check_files()
                if changed_files:
                    logging.info(f"检测到文件变化: {', '.join(changed_files)}")
                    try:
                        self.callback(changed_files)
                    except Exception as e:
                        logging.error(f"执行回调函数失败: {str(e)}")
            except Exception as e:
                logging.error(f"文件监控循环出错: {str(e)}")

            # 等待下次检查
            time.sleep(self.check_interval)

    def _check_files(self):
        """检查文件变化"""
        changed_files = []

        # 检查文件路径是否有变化
        current_paths = self.file_paths.copy()
        for file_path in list(self.file_stats.keys()):
            if file_path not in current_paths:
                logging.info(f"文件路径已从监控列表中移除: {file_path}")
                del self.file_stats[file_path]

        for file_path in current_paths:
            try:
                # 检查文件是否存在
                exists = os.path.exists(file_path)

                # 如果文件路径不在状态字典中，添加它
                if file_path not in self.file_stats:
                    if exists:
                        stat = os.stat(file_path)
                        self.file_stats[file_path] = {
                            'mtime': stat.st_mtime,
                            'size': stat.st_size,
                            'exists': True,
                            'last_checked': datetime.now()
                        }
                        changed_files.append(file_path)
                        logging.info(f"新增监控文件: {file_path}")
                    else:
                        self.file_stats[file_path] = {
                            'mtime': 0,
                            'size': 0,
                            'exists': False,
                            'last_checked': datetime.now()
                        }
                        logging.info(f"新增监控文件（不存在）: {file_path}")
                    continue

                # 文件状态变化：不存在 -> 存在
                if not self.file_stats[file_path]['exists'] and exists:
                    stat = os.stat(file_path)
                    self.file_stats[file_path] = {
                        'mtime': stat.st_mtime,
                        'size': stat.st_size,
                        'exists': True,
                        'last_checked': datetime.now()
                    }
                    changed_files.append(file_path)
                    logging.info(f"文件已创建: {file_path}")

                # 文件状态变化：存在 -> 不存在
                elif self.file_stats[file_path]['exists'] and not exists:
                    self.file_stats[file_path] = {
                        'mtime': 0,
                        'size': 0,
                        'exists': False,
                        'last_checked': datetime.now()
                    }
                    changed_files.append(file_path)
                    logging.info(f"文件已删除: {file_path}")

                # 文件存在，检查是否修改
                elif exists:
                    stat = os.stat(file_path)
                    if (stat.st_mtime > self.file_stats[file_path]['mtime'] or
                        stat.st_size != self.file_stats[file_path]['size']):
                        # 记录旧的文件大小
                        old_size = self.file_stats[file_path]['size']

                        # 更新文件状态
                        self.file_stats[file_path] = {
                            'mtime': stat.st_mtime,
                            'size': stat.st_size,
                            'exists': True,
                            'last_checked': datetime.now()
                        }

                        # 记录文件大小变化
                        size_change = stat.st_size - old_size
                        size_change_str = f"+{size_change}" if size_change > 0 else str(size_change)

                        changed_files.append(file_path)
                        logging.info(f"文件已修改: {file_path} (大小变化: {size_change_str} 字节)")

                # 更新最后检查时间
                self.file_stats[file_path]['last_checked'] = datetime.now()

            except Exception as e:
                logging.error(f"检查文件变化失败: {file_path}, 错误: {str(e)}")

        return changed_files

    def force_check(self):
        """强制检查文件变化"""
        changed_files = self._check_files()
        if changed_files:
            logging.info(f"强制检查检测到文件变化: {', '.join(changed_files)}")
            try:
                self.callback(changed_files)
            except Exception as e:
                logging.error(f"执行回调函数失败: {str(e)}")
        return changed_files

# 全局实例
_file_watchers = {}

def get_file_watcher(file_paths, callback, check_interval=60):
    """获取文件监控器实例"""
    global _file_watchers

    # 生成唯一键
    key = str(file_paths) if isinstance(file_paths, list) else file_paths

    if key not in _file_watchers:
        _file_watchers[key] = FileWatcher(file_paths, callback, check_interval)

    return _file_watchers[key]
