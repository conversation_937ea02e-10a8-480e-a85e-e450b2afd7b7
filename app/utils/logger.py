#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志模块 - 使用 loguru 处理日志配置和输出
"""

import os
import sys
from loguru import logger
from app.utils.config import get_config_instance

def disable_standard_logging():
    """禁用标准 logging 模块，防止创建额外的日志文件"""
    import logging

    # 获取根日志器
    root_logger = logging.getLogger()

    # 移除所有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        try:
            handler.close()
        except:
            pass

    # 清空处理器列表
    root_logger.handlers.clear()

    # 设置最高级别，禁用所有日志
    root_logger.setLevel(logging.CRITICAL + 1)
    root_logger.disabled = True

    # 完全禁用 logging 模块
    logging.disable(logging.CRITICAL)

    # 重写 basicConfig 函数，防止其他代码重新配置
    def dummy_basic_config(*args, **kwargs):
        pass

    logging.basicConfig = dummy_basic_config

def setup_logging(config_file='app/config/config.ini'):
    """设置 loguru 日志"""
    # 首先禁用标准 logging
    disable_standard_logging()

    config = get_config_instance(config_file)

    # 获取日志配置
    log_level_str = config.get('general', 'log_level', fallback='INFO').upper()
    log_file = config.get('general', 'log_file', fallback='')

    # 确保日志目录存在
    os.makedirs(os.path.dirname(log_file), exist_ok=True)

    # 移除 loguru 默认的处理器
    logger.remove()

    # 添加控制台处理器（带颜色，统一时间格式）
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=log_level_str,
        colorize=True
    )

    # 生成基于日期的日志文件名
    log_dir = os.path.dirname(log_file)
    log_name = os.path.splitext(os.path.basename(log_file))[0]
    daily_log_file = os.path.join(log_dir, f"{log_name}_{'{time:YYYY-MM-DD}'}.log")

    # 添加文件处理器（不带颜色，按日期分割，统一时间格式）
    logger.add(
        daily_log_file,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
        level=log_level_str,
        rotation="00:00",  # 每天午夜轮转
        retention="30 days",  # 保留30天
        compression="zip",
        encoding="utf-8",
        enqueue=True,  # 启用异步写入
        catch=True     # 捕获异常
    )

    logger.debug(f"日志系统已初始化，级别: {log_level_str}, 文件模式: 按日期分割")

    # 输出配置加载确认（在日志系统初始化后）
    logger.info(f"已加载配置文件: {config_file}")

    # 输出配置初始化信息
    try:
        config = get_config_instance(config_file)
        config.log_initialization_info()
    except Exception as e:
        logger.error(f"输出配置初始化信息失败: {e}")

    return logger

def get_logger():
    """获取 loguru logger 实例"""
    return logger
