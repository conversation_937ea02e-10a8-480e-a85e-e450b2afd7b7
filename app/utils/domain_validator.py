#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
域名验证和信息获取工具
"""

import socket
import logging
import requests
import re
import time
import random
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from bs4 import BeautifulSoup
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# 禁用不安全请求的警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class DomainValidator:
    """域名验证和信息获取类"""
    
    def __init__(self, timeout=10, max_workers=10, max_retries=2):
        """初始化域名验证器"""
        self.timeout = timeout
        self.max_workers = max_workers
        self.max_retries = max_retries
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
        ]
    
    def validate_domain(self, domain):
        """验证域名是否有效（可解析）"""
        try:
            # 尝试解析域名
            socket.gethostbyname(domain)
            return True
        except socket.gaierror:
            logging.debug(f"域名无法解析: {domain}")
            return False
        except Exception as e:
            logging.warning(f"验证域名时出错: {domain}, 错误: {str(e)}")
            return False
    
    def get_domain_info(self, domain):
        """获取域名的网站信息（标题和body大小）"""
        if not self.validate_domain(domain):
            return {
                'valid': False,
                'title': None,
                'body_size': None,
                'status_code': None,
                'error': '域名无法解析'
            }
        
        # 尝试HTTP和HTTPS
        protocols = ['https', 'http']
        
        for protocol in protocols:
            url = f"{protocol}://{domain}"
            result = self._fetch_url_info(url)
            
            # 如果成功获取信息，直接返回
            if result['valid']:
                return result
        
        # 如果所有尝试都失败
        return {
            'valid': False,
            'title': None,
            'body_size': None,
            'status_code': None,
            'error': '无法连接到网站'
        }
    
    def _fetch_url_info(self, url):
        """获取URL的信息"""
        for attempt in range(self.max_retries):
            try:
                # 随机选择一个User-Agent
                headers = {
                    'User-Agent': random.choice(self.user_agents),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Connection': 'close'  # 避免保持连接
                }
                
                # 发送请求
                response = requests.get(
                    url, 
                    headers=headers, 
                    timeout=self.timeout, 
                    verify=False,  # 不验证SSL证书
                    allow_redirects=True  # 允许重定向
                )
                
                # 获取响应内容
                body = response.text
                body_size = len(response.content)
                
                # 提取标题
                title = None
                try:
                    soup = BeautifulSoup(body, 'html.parser')
                    title_tag = soup.find('title')
                    if title_tag:
                        title = title_tag.text.strip()
                except Exception as e:
                    logging.debug(f"提取标题时出错: {url}, 错误: {str(e)}")
                
                return {
                    'valid': True,
                    'title': title,
                    'body_size': body_size,
                    'status_code': response.status_code,
                    'url': response.url  # 可能经过重定向
                }
                
            except requests.exceptions.Timeout:
                logging.debug(f"请求超时: {url} (尝试 {attempt+1}/{self.max_retries})")
            except requests.exceptions.ConnectionError:
                logging.debug(f"连接错误: {url} (尝试 {attempt+1}/{self.max_retries})")
            except requests.exceptions.RequestException as e:
                logging.debug(f"请求错误: {url}, 错误: {str(e)} (尝试 {attempt+1}/{self.max_retries})")
            except Exception as e:
                logging.debug(f"获取URL信息时出错: {url}, 错误: {str(e)} (尝试 {attempt+1}/{self.max_retries})")
            
            # 如果不是最后一次尝试，等待一段时间后重试
            if attempt < self.max_retries - 1:
                time.sleep(1)
        
        # 所有尝试都失败
        return {
            'valid': False,
            'title': None,
            'body_size': None,
            'status_code': None,
            'error': '请求失败'
        }
    
    def batch_validate_domains(self, domains):
        """批量验证域名"""
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_domain = {executor.submit(self.validate_domain, domain): domain for domain in domains}
            for future in as_completed(future_to_domain):
                domain = future_to_domain[future]
                try:
                    results[domain] = future.result()
                except Exception as e:
                    logging.warning(f"验证域名时出错: {domain}, 错误: {str(e)}")
                    results[domain] = False
        
        return results
    
    def batch_get_domain_info(self, domains):
        """批量获取域名信息"""
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_domain = {executor.submit(self.get_domain_info, domain): domain for domain in domains}
            for future in as_completed(future_to_domain):
                domain = future_to_domain[future]
                try:
                    results[domain] = future.result()
                except Exception as e:
                    logging.warning(f"获取域名信息时出错: {domain}, 错误: {str(e)}")
                    results[domain] = {
                        'valid': False,
                        'title': None,
                        'body_size': None,
                        'status_code': None,
                        'error': str(e)
                    }
        
        return results

# 全局实例
_validator_instance = None

def get_validator_instance():
    """获取域名验证器实例"""
    global _validator_instance
    if _validator_instance is None:
        _validator_instance = DomainValidator()
    return _validator_instance
