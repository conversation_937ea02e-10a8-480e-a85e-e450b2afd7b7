#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理模块 - 处理配置文件的读取和解析
"""

import os
import glob
import threading
import time
from datetime import datetime
from configparser import ConfigParser
from loguru import logger

class ConfigManager:
    """配置管理类"""

    def __init__(self, config_file='app/config/config.ini'):
        """初始化配置管理器"""

        self.config_file = config_file
        self.config = ConfigParser()

        # 设置默认值
        self._set_defaults()

        # 读取配置文件
        if os.path.exists(config_file):
            self.config.read(config_file)
            # 延迟日志输出，避免在日志系统初始化前输出
            # logger.info(f"已加载配置文件: {config_file}")
        else:
            logger.warning(f"配置文件不存在: {config_file}，使用默认配置")
            # 创建默认配置文件
            self._create_default_config()

        # 域名文件监控
        self.domain_files_last_check = {}
        self.domain_files_content = {}
        self.domain_files_callbacks = []
        # 获取域名文件检查间隔（配置中是分钟，转换为秒）
        self.domain_files_check_interval = self.getint('domains', 'file_check_interval', fallback=60) * 60
        self.domain_files_monitor_enabled = self.getboolean('domains', 'dynamic_loading', fallback=True)

        # 初始化域名文件状态
        self._init_domain_files_state()

    def log_initialization_info(self):
        """在日志系统初始化后输出初始化信息"""
        # 输出域名文件发现信息
        domain_files = self.get_domain_files()
        if domain_files:
            logger.info(f"找到 {len(domain_files)} 个域名文件:")
            for file_path in domain_files:
                logger.info(f"  - {file_path}")
        else:
            logger.warning("未找到任何域名文件")

    def _set_defaults(self):
        """设置默认配置"""
        self.config['general'] = {
            'log_level': 'INFO',
            'log_file': 'logs/certmonitor.log',
            'color_output': 'true'
        }

        self.config['domains'] = {
            'domain_files': 'domains.txt, app/config/custom_domains.txt',
            'recursive_search': 'false',
            'domain_file_extensions': '.txt, .list, .domains',
            'scan_new_domains': 'true',
            'notify_on_first_run': 'false',
            'dynamic_loading': 'true',
            'file_check_interval': '60',  # 分钟
            'auto_remove_domains': 'true'
        }

        self.config['database'] = {
            'mysql_host': 'localhost',
            'mysql_port': '3306',
            'mysql_user': 'root',
            'mysql_password': '',
            'mysql_database': 'certmonitor',
            'connect_timeout': '10',
            'max_retries': '3',
            'retry_delay': '5',
            'pool_size': '5',
            'pool_name': 'certmonitor_pool'
        }

        # Let's Encrypt CT日志监控配置
        self.config['letsencrypt'] = {
            'enabled': 'true',
            'oak_2025h1_url': 'https://oak.ct.letsencrypt.org/2025h1/',
            'oak_2025h2_url': 'https://oak.ct.letsencrypt.org/2025h2/',
            'check_interval': '180',
            'timeout': '30'
        }

        # Cloudflare CT日志监控配置
        self.config['cloudflare'] = {
            'enabled': 'true',
            'nimbus_2025_url': 'https://ct.cloudflare.com/logs/nimbus2025/',
            'check_interval': '180',
            'timeout': '30'
        }

        self.config['crtsh'] = {
            'enabled': 'true',
            'interval': '24',
            'timeout': '60',
            'retries': '3',
            'delay': '2',
            'concurrency': '5'
        }

        self.config['notification'] = {
            'enabled': 'true',
            'check_interval': '60',
            'batch_size': '10',
            'batch_timeout': '300'
        }

        self.config['wechat'] = {
            'enabled': 'false',
            'webhook_url': '',
            'timeout': '10',
            'retries': '3',
            'delay': '2'
        }

    def _create_default_config(self):
        """创建默认配置文件"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        with open(self.config_file, 'w') as f:
            self.config.write(f)
        logger.info(f"已创建默认配置文件: {self.config_file}")

    def get(self, section, option, fallback=None):
        """获取配置项的值"""
        return self.config.get(section, option, fallback=fallback)

    def getint(self, section, option, fallback=None):
        """获取整数类型的配置项值"""
        return self.config.getint(section, option, fallback=fallback)

    def getfloat(self, section, option, fallback=None):
        """获取浮点数类型的配置项值"""
        return self.config.getfloat(section, option, fallback=fallback)

    def getboolean(self, section, option, fallback=None):
        """获取布尔类型的配置项值"""
        return self.config.getboolean(section, option, fallback=fallback)

    def set(self, section, option, value):
        """设置配置项的值"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][option] = value

    def get_domain_files(self):
        """获取所有域名文件路径"""
        domain_files = []

        # 获取配置的域名文件路径
        file_paths = self.get('domains', 'domain_files', fallback='domains.txt')
        file_paths = [p.strip() for p in file_paths.split(',')]

        # 获取文件扩展名
        extensions = self.get('domains', 'domain_file_extensions', fallback='.txt')
        extensions = [e.strip() for e in extensions.split(',')]

        # 是否递归搜索
        recursive = self.getboolean('domains', 'recursive_search', fallback=False)

        # 是否加载自定义域名文件
        load_custom = self.getboolean('domains', 'load_custom_domains', fallback=False)

        # 如果启用了自定义域名文件，添加到文件路径列表中
        if load_custom and os.path.isfile('app/config/custom_domains.txt'):
            file_paths.append('app/config/custom_domains.txt')
            logger.info("已启用自定义域名文件: app/config/custom_domains.txt")

        # 简化日志输出，只在需要时显示关键信息

        for path in file_paths:
            # 如果是目录，搜索目录中的文件
            if os.path.isdir(path):
                for ext in extensions:
                    if recursive:
                        # 递归搜索
                        for file_path in glob.glob(os.path.join(path, f"**/*{ext}"), recursive=True):
                            if os.path.isfile(file_path):
                                domain_files.append(file_path)
                    else:
                        # 只搜索当前目录
                        for file_path in glob.glob(os.path.join(path, f"*{ext}")):
                            if os.path.isfile(file_path):
                                domain_files.append(file_path)
            # 如果是文件，直接添加
            elif os.path.isfile(path):
                domain_files.append(path)
            # 如果是通配符路径，搜索匹配的文件
            else:
                for file_path in glob.glob(path):
                    if os.path.isfile(file_path):
                        domain_files.append(file_path)

        # 确保路径存在且唯一
        unique_files = []
        for file_path in domain_files:
            if file_path not in unique_files:
                unique_files.append(file_path)

        # 打印找到的所有域名文件（移到DEBUG级别，避免在日志系统初始化前输出）
        if unique_files:
            if not hasattr(self, '_domain_files_logged'):
                # 延迟到日志系统初始化后再输出详细信息
                # logger.info(f"找到 {len(unique_files)} 个域名文件:")
                # for file_path in unique_files:
                #     logger.info(f"  - {file_path}")
                self._domain_files_logged = True
            else:
                logger.debug(f"找到 {len(unique_files)} 个域名文件")
        else:
            logger.warning("未找到任何域名文件")

        return unique_files

    def _init_domain_files_state(self):
        """初始化域名文件状态"""
        for file_path in self.get_domain_files():
            try:
                if os.path.exists(file_path):
                    stat = os.stat(file_path)
                    self.domain_files_last_check[file_path] = {
                        'mtime': stat.st_mtime,
                        'size': stat.st_size,
                        'last_checked': datetime.now()
                    }

                    # 加载文件内容
                    with open(file_path, 'r') as f:
                        content = f.read()
                    self.domain_files_content[file_path] = content
                else:
                    self.domain_files_last_check[file_path] = {
                        'mtime': 0,
                        'size': 0,
                        'last_checked': datetime.now()
                    }
                    self.domain_files_content[file_path] = ""
            except Exception as e:
                logger.error(f"初始化域名文件状态失败: {file_path}, 错误: {str(e)}")

    def register_domain_files_callback(self, callback):
        """注册域名文件变化回调函数"""
        if callback not in self.domain_files_callbacks:
            self.domain_files_callbacks.append(callback)
            logger.info(f"已注册域名文件变化回调函数: {callback.__name__}")

    def unregister_domain_files_callback(self, callback):
        """注销域名文件变化回调函数"""
        if callback in self.domain_files_callbacks:
            self.domain_files_callbacks.remove(callback)
            logger.info(f"已注销域名文件变化回调函数: {callback.__name__}")

    def check_domain_files_changes(self):
        """检查域名文件变化"""
        if not self.domain_files_monitor_enabled:
            return False

        changed_files = []

        # 获取当前所有域名文件
        current_files = self.get_domain_files()

        # 检查是否有文件被删除
        for file_path in list(self.domain_files_last_check.keys()):
            if file_path not in current_files:
                # 文件已从配置中移除
                if self.domain_files_last_check[file_path]['size'] > 0:
                    self.domain_files_last_check[file_path] = {
                        'mtime': 0,
                        'size': 0,
                        'last_checked': datetime.now()
                    }
                    self.domain_files_content[file_path] = ""
                    changed_files.append(file_path)
                    logger.info(f"域名文件已从配置中移除: {file_path}")

        for file_path in current_files:
            try:
                # 检查文件是否存在
                if os.path.exists(file_path):
                    stat = os.stat(file_path)

                    # 检查文件是否已记录
                    if file_path not in self.domain_files_last_check:
                        # 新文件
                        self.domain_files_last_check[file_path] = {
                            'mtime': stat.st_mtime,
                            'size': stat.st_size,
                            'last_checked': datetime.now()
                        }

                        # 加载文件内容
                        with open(file_path, 'r') as f:
                            content = f.read()
                        self.domain_files_content[file_path] = content

                        changed_files.append(file_path)
                        logger.info(f"发现新域名文件: {file_path}")
                    else:
                        # 检查文件是否被修改
                        last_check = self.domain_files_last_check[file_path]
                        if (stat.st_mtime > last_check['mtime'] or
                            stat.st_size != last_check['size']):
                            # 文件已修改
                            self.domain_files_last_check[file_path] = {
                                'mtime': stat.st_mtime,
                                'size': stat.st_size,
                                'last_checked': datetime.now()
                            }

                            # 加载文件内容
                            with open(file_path, 'r') as f:
                                content = f.read()

                            # 检查内容是否真的变化
                            if content != self.domain_files_content.get(file_path, ""):
                                self.domain_files_content[file_path] = content
                                changed_files.append(file_path)
                                logger.info(f"域名文件已修改: {file_path}")
                else:
                    # 文件不存在，但之前存在
                    if file_path in self.domain_files_last_check and self.domain_files_last_check[file_path]['size'] > 0:
                        self.domain_files_last_check[file_path] = {
                            'mtime': 0,
                            'size': 0,
                            'last_checked': datetime.now()
                        }
                        self.domain_files_content[file_path] = ""
                        changed_files.append(file_path)
                        logger.info(f"域名文件已删除: {file_path}")
            except Exception as e:
                logger.error(f"检查域名文件变化失败: {file_path}, 错误: {str(e)}")

        # 如果有文件变化，触发回调
        if changed_files and self.domain_files_callbacks:
            for callback in self.domain_files_callbacks:
                try:
                    callback(changed_files)
                except Exception as e:
                    logger.error(f"执行域名文件变化回调函数失败: {callback.__name__}, 错误: {str(e)}")

        return bool(changed_files)

    def start_domain_files_monitor(self):
        """启动域名文件监控"""
        if not self.domain_files_monitor_enabled:
            logger.info("域名文件动态加载已禁用")
            return

        def monitor_loop():
            while self.domain_files_monitor_enabled:
                try:
                    self.check_domain_files_changes()
                except Exception as e:
                    logger.error(f"域名文件监控出错: {str(e)}")

                time.sleep(self.domain_files_check_interval)

        # 启动监控线程
        threading.Thread(target=monitor_loop, daemon=True).start()
        # 显示分钟
        logger.info(f"域名文件监控已启动，检查间隔: {self.domain_files_check_interval // 60}分钟")

    def load_domains_from_files(self):
        """从所有域名文件中加载域名"""
        domains = set()
        domain_sources = {}  # 记录每个域名来自哪个文件

        # 获取所有域名文件
        domain_files = self.get_domain_files()
        logger.debug(f"准备从 {len(domain_files)} 个域名文件中加载域名")

        for file_path in domain_files:
            file_domains = set()
            try:
                with open(file_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            domain = line.lower()
                            domains.add(domain)
                            file_domains.add(domain)
                            # 记录域名来源
                            if domain not in domain_sources:
                                domain_sources[domain] = []
                            domain_sources[domain].append(file_path)
                logger.debug(f"已从 {file_path} 加载 {len(file_domains)} 个域名")
            except Exception as e:
                logger.error(f"加载域名文件 {file_path} 失败: {str(e)}")

        # 记录每个域名的来源
        for domain, sources in domain_sources.items():
            if len(sources) > 1:
                logger.debug(f"域名 {domain} 来自多个文件: {', '.join(sources)}")

        sorted_domains = sorted(list(domains))
        logger.debug(f"总共加载了 {len(sorted_domains)} 个唯一域名")
        return sorted_domains

# 全局实例（保持向后兼容）
_config_instance = None

def get_config_instance(config_file='app/config/config.ini'):
    """获取配置实例（保持向后兼容，但不再强制单例）"""
    global _config_instance
    if _config_instance is None:
        _config_instance = ConfigManager(config_file)
    return _config_instance
