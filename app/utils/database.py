#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库管理模块 - 处理数据库操作和连接池
"""

import os
import time
from loguru import logger
import threading
import mysql.connector
from mysql.connector import Error as MySQLError
from mysql.connector.pooling import MySQLConnectionPool

class DatabaseManager:
    """数据库管理类，包含连接池功能"""

    def __init__(self, config):
        """初始化数据库管理器"""
        self.config = config

        # 获取MySQL配置
        self.host = config.get('database', 'mysql_host', fallback='localhost')
        self.port = config.getint('database', 'mysql_port', fallback=3306)
        self.user = config.get('database', 'mysql_user', fallback='root')
        self.password = config.get('database', 'mysql_password', fallback='')
        self.database = config.get('database', 'mysql_database', fallback='certmonitor')
        self.connect_timeout = config.getint('database', 'connect_timeout', fallback=10)
        self.max_retries = config.getint('database', 'max_retries', fallback=3)
        self.retry_delay = config.getint('database', 'retry_delay', fallback=5)
        self.pool_size = config.getint('database', 'pool_size', fallback=5)
        self.pool_name = config.get('database', 'pool_name', fallback='certmonitor_pool')

        # 创建连接池
        self._create_pool()

    def _create_pool(self):
        """创建数据库连接池"""
        retries = 0
        last_error = None

        while retries < self.max_retries:
            try:
                # 首先尝试连接到MySQL服务器（不指定数据库）
                logger.debug(f"尝试连接到MySQL服务器 {self.host}:{self.port}...")
                conn = mysql.connector.connect(
                    host=self.host,
                    port=self.port,
                    user=self.user,
                    password=self.password,
                    connection_timeout=self.connect_timeout
                )
                cursor = conn.cursor()

                # 检查数据库是否存在
                cursor.execute(f"SHOW DATABASES LIKE '{self.database}'")
                result = cursor.fetchone()

                # 如果数据库不存在，则创建
                if not result:
                    logger.info(f"数据库 {self.database} 不存在，正在创建...")
                    cursor.execute(f"CREATE DATABASE {self.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                    logger.info(f"数据库 {self.database} 创建成功")

                # 关闭连接
                cursor.close()
                conn.close()

                # 创建连接池
                logger.debug(f"正在创建数据库连接池，池大小: {self.pool_size}...")
                self.pool = MySQLConnectionPool(
                    pool_name=self.pool_name,
                    pool_size=self.pool_size,
                    host=self.host,
                    port=self.port,
                    user=self.user,
                    password=self.password,
                    database=self.database,
                    connect_timeout=self.connect_timeout
                )
                logger.debug(f"数据库连接池创建成功")

                # 初始化数据库表
                self._init_tables()

                return

            except MySQLError as e:
                last_error = e
                retries += 1
                logger.error(f"MySQL连接错误 (尝试 {retries}/{self.max_retries}): {str(e)}")

                if retries < self.max_retries:
                    logger.info(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)

        # 如果所有重试都失败，则抛出异常
        logger.critical(f"无法连接到MySQL数据库，已达到最大重试次数: {self.max_retries}")
        raise MySQLError(f"无法连接到MySQL数据库: {str(last_error)}")

    def _init_tables(self):
        """初始化数据库表"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 创建域名表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS domains (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL UNIQUE,
                source VARCHAR(255),
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX (name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ''')

            # 创建子域名表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS subdomains (
                id INT AUTO_INCREMENT PRIMARY KEY,
                domain_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                source VARCHAR(50),
                discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                first_seen DATE NULL,
                FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
                UNIQUE KEY (domain_id, name),
                INDEX (name),
                INDEX (first_seen)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ''')

            # 创建变更记录表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS changes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                domain_id INT NOT NULL,
                subdomain_id INT NOT NULL,
                action ENUM('add', 'remove') NOT NULL,
                source VARCHAR(50),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notified BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
                FOREIGN KEY (subdomain_id) REFERENCES subdomains(id) ON DELETE CASCADE,
                INDEX (timestamp)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ''')

            conn.commit()
            logger.debug("数据库表初始化成功")

        except MySQLError as e:
            logger.error(f"初始化数据库表失败: {str(e)}")
            conn.rollback()
            raise
        finally:
            cursor.close()
            conn.close()

    def get_connection(self):
        """获取数据库连接"""
        try:
            return self.pool.get_connection()
        except MySQLError as e:
            logger.error(f"获取数据库连接失败: {str(e)}")
            # 尝试重新创建连接池
            self._create_pool()
            return self.pool.get_connection()

    def execute_query(self, query, params=None, fetch=False, commit=True):
        """执行SQL查询"""
        conn = None
        cursor = None

        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)

            # 执行查询
            cursor.execute(query, params)

            # 提交事务
            if commit:
                conn.commit()

            # 获取结果
            if fetch:
                return cursor.fetchall()
            elif query.strip().upper().startswith("INSERT"):
                return cursor.lastrowid
            elif query.strip().upper().startswith("SELECT"):
                return cursor.fetchone()
            else:
                return cursor.rowcount

        except MySQLError as e:
            if conn:
                conn.rollback()
            logger.error(f"执行查询失败: {str(e)}")
            raise

        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    def add_domain(self, domain_name, source=None):
        """添加新域名到监控列表"""
        try:
            query = "INSERT INTO domains (name, source) VALUES (%s, %s)"
            return self.execute_query(query, (domain_name.lower(), source))
        except MySQLError as e:
            # 如果是重复键错误，则获取已存在的域名ID
            if hasattr(e, 'errno') and e.errno == 1062:  # ER_DUP_ENTRY
                logger.debug(f"域名已存在: {domain_name}")
                return self.get_domain_id(domain_name)
            raise

    def get_domain_id(self, domain_name):
        """通过域名获取ID"""
        query = "SELECT id FROM domains WHERE name = %s"
        result = self.execute_query(query, (domain_name.lower(),), commit=False)

        if result:
            return result['id']
        return None

    def remove_domain(self, domain_name):
        """从监控列表中移除域名"""
        domain_name = domain_name.lower()
        domain_id = self.get_domain_id(domain_name)

        if not domain_id:
            logger.debug(f"数据库中未找到域名: {domain_name}")
            return False

        # 获取子域名数量
        subdomains = self.get_subdomains(domain_id)
        subdomain_count = len(subdomains) if subdomains else 0

        # 记录删除操作
        logger.debug(f"准备从数据库中删除域名: {domain_name} (ID: {domain_id}, 子域名数量: {subdomain_count})")

        # 删除域名（级联删除子域名和变更记录）
        query = "DELETE FROM domains WHERE id = %s"
        rows_affected = self.execute_query(query, (domain_id,))

        if rows_affected > 0:
            logger.debug(f"成功从数据库中删除域名: {domain_name} (ID: {domain_id}, 子域名数量: {subdomain_count})")
            return True
        else:
            logger.debug(f"从数据库中删除域名失败: {domain_name} (ID: {domain_id})")
            return False

    def get_all_domains(self):
        """获取所有监控的域名"""
        query = """
        SELECT d.id, d.name, d.source, d.added_at, d.last_updated, COUNT(s.id) as subdomain_count
        FROM domains d
        LEFT JOIN subdomains s ON d.id = s.domain_id
        GROUP BY d.id
        ORDER BY d.name
        """

        return self.execute_query(query, fetch=True, commit=False)

    def add_subdomain(self, domain_id, subdomain_name, source=None, first_seen=None):
        """添加新的子域名，返回变更ID"""
        try:
            # 插入子域名
            query = "INSERT INTO subdomains (domain_id, name, source, first_seen) VALUES (%s, %s, %s, %s)"
            subdomain_id = self.execute_query(query, (domain_id, subdomain_name.lower(), source, first_seen))

            # 更新域名的最后更新时间
            update_query = "UPDATE domains SET last_updated = CURRENT_TIMESTAMP WHERE id = %s"
            self.execute_query(update_query, (domain_id,))

            # 记录变更并返回变更ID
            change_id = self.add_change_record(domain_id, subdomain_id, 'add', source)

            return change_id
        except MySQLError as e:
            # 如果是重复键错误，则返回None表示没有新增
            if hasattr(e, 'errno') and e.errno == 1062:  # ER_DUP_ENTRY
                logger.debug(f"子域名已存在: {subdomain_name}")
                return None
            raise

    def get_subdomain_id(self, domain_id, subdomain_name):
        """获取子域名ID"""
        query = "SELECT id FROM subdomains WHERE domain_id = %s AND name = %s"
        result = self.execute_query(query, (domain_id, subdomain_name.lower()), commit=False)

        if result:
            return result['id']
        return None

    def get_subdomains(self, domain_id):
        """获取指定域名的所有子域名"""
        query = """
        SELECT id, name, source, discovered_at
        FROM subdomains
        WHERE domain_id = %s
        ORDER BY name
        """

        return self.execute_query(query, (domain_id,), fetch=True, commit=False)

    def is_subdomain_exists(self, domain_id, subdomain_name):
        """检查子域名是否已存在"""
        return self.get_subdomain_id(domain_id, subdomain_name.lower()) is not None

    def add_change_record(self, domain_id, subdomain_id, action, source=None):
        """添加变更记录，返回变更ID"""
        query = "INSERT INTO changes (domain_id, subdomain_id, action, source) VALUES (%s, %s, %s, %s)"
        change_id = self.execute_query(query, (domain_id, subdomain_id, action, source))
        return change_id

    def get_recent_changes(self, limit=100):
        """获取最近的变更记录"""
        query = """
        SELECT c.id, d.name as domain, s.name as subdomain, c.action, c.source, c.timestamp, c.notified
        FROM changes c
        JOIN domains d ON c.domain_id = d.id
        JOIN subdomains s ON c.subdomain_id = s.id
        ORDER BY c.timestamp DESC
        LIMIT %s
        """

        return self.execute_query(query, (limit,), fetch=True, commit=False)

    def get_unnotified_changes(self):
        """获取未通知的变更记录"""
        query = """
        SELECT c.id, d.name as domain, s.name as subdomain, c.action, c.source, c.timestamp
        FROM changes c
        JOIN domains d ON c.domain_id = d.id
        JOIN subdomains s ON c.subdomain_id = s.id
        WHERE c.notified = FALSE
        ORDER BY c.timestamp ASC
        """

        return self.execute_query(query, fetch=True, commit=False)

    def mark_changes_as_notified(self, change_ids):
        """将变更记录标记为已通知"""
        if not change_ids:
            return 0

        placeholders = ', '.join(['%s'] * len(change_ids))
        query = f"UPDATE changes SET notified = TRUE WHERE id IN ({placeholders})"

        return self.execute_query(query, change_ids)

# 全局实例（保持向后兼容）
_db_instance = None

def get_db_instance(config_file='app/config/config.ini'):
    """获取数据库管理器实例（保持向后兼容）"""
    global _db_instance
    if _db_instance is None:
        from app.utils.config import get_config_instance
        config = get_config_instance(config_file)
        _db_instance = DatabaseManager(config)
    return _db_instance
