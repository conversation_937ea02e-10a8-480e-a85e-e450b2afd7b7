#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CertMonitor - 使用certstream和crt.sh监控子域名新增

该工具使用certstream库连接到证书透明度日志的实时流，
并定期查询crt.sh网站，监控指定域名的新增子域名。
当发现新的子域名时，会立即通知用户并通过微信发送通知。

支持MySQL数据库存储，使用连接池提高性能，
可以从多个文件中读取要监控的域名，
并记录每次发现的新子域名。
"""

# 在导入任何其他模块之前，先彻底禁用标准 logging
from logging_killer import ensure_logging_killed
ensure_logging_killed()

import os
import time
import argparse
from datetime import datetime
from termcolor import colored

# 导入CertMonitor模块
from app.utils.logger import setup_logging, get_logger
from app.utils.config import get_config_instance
from app.core.manager import get_monitor_manager

# 全局变量
VERSION = "2.0.0"

def print_colored(message, color=None, enabled=True):
    """打印彩色文本"""
    if enabled and color:
        print(colored(message, color))
    else:
        print(message)

def add_domain(args):
    """添加要监控的域名"""
    config = get_config_instance(args.config)
    manager = get_monitor_manager(args.config)
    color_enabled = config.getboolean('general', 'color_output', fallback=True)

    if manager.add_domain(args.add, source='manual'):
        print_colored(f"[+] 已添加 {args.add} 到监控列表", "green", color_enabled)
    else:
        print_colored(f"[!] 域名 {args.add} 已在监控列表中", "yellow", color_enabled)

def add_domains_from_files(args):
    """从配置的文件中添加域名"""
    config = get_config_instance(args.config)
    manager = get_monitor_manager(args.config)
    color_enabled = config.getboolean('general', 'color_output', fallback=True)

    added_count = manager.add_domains_from_files()

    if added_count > 0:
        print_colored(f"[+] 已从文件中添加 {added_count} 个域名到监控列表", "green", color_enabled)
    else:
        print_colored("[*] 所有域名已在监控列表中", "blue", color_enabled)

def remove_domain(args):
    """从监控列表中移除域名"""
    config = get_config_instance(args.config)
    manager = get_monitor_manager(args.config)
    color_enabled = config.getboolean('general', 'color_output', fallback=True)

    if manager.remove_domain(args.remove):
        print_colored(f"[+] 已从监控列表中移除 {args.remove}", "green", color_enabled)
    else:
        print_colored(f"[!] 域名 {args.remove} 不在监控列表中", "yellow", color_enabled)

def list_domains(args):
    """列出所有监控的域名"""
    config = get_config_instance(args.config)
    manager = get_monitor_manager(args.config)
    color_enabled = config.getboolean('general', 'color_output', fallback=True)

    domains = manager.list_domains()

    if not domains:
        print_colored("[!] 监控列表为空", "yellow", color_enabled)
        return

    print_colored(f"\n[*] 监控的域名列表 (共 {len(domains)} 个):", "blue", color_enabled)

    # 如果域名太多，只显示前100个
    display_limit = 100
    if len(domains) > display_limit:
        print_colored(f"[*] 仅显示前 {display_limit} 个域名", "yellow", color_enabled)

    for i, domain in enumerate(domains):
        if i >= display_limit:
            break

        name = domain['name']
        count = domain['subdomain_count']
        source = domain['source'] or '未知'
        added_at = domain['added_at']

        print_colored(f"  - {name} (子域名数量: {count}, 来源: {source}, 添加时间: {added_at})", "cyan", color_enabled)

    print()

def export_subdomains(args):
    """导出指定域名的所有子域名"""
    config = get_config_instance(args.config)
    manager = get_monitor_manager(args.config)
    color_enabled = config.getboolean('general', 'color_output', fallback=True)

    subdomains = manager.export_subdomains(args.export)

    if subdomains is None:
        print_colored(f"[!] 域名 {args.export} 不在监控列表中", "yellow", color_enabled)
        return

    if not subdomains:
        print_colored(f"[!] 域名 {args.export} 没有记录的子域名", "yellow", color_enabled)
        return

    # 获取导出目录配置
    export_dir = config.get('export', 'export_dir', fallback='exports')
    os.makedirs(export_dir, exist_ok=True)

    # 导出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    export_file = os.path.join(export_dir, f"{args.export}_{timestamp}.txt")

    # 写入文件
    with open(export_file, 'w') as f:
        for subdomain in subdomains:
            f.write(f"{subdomain['name']}\n")

    print_colored(f"[+] 已导出 {len(subdomains)} 个子域名到 {export_file}", "green", color_enabled)

def search_domain(args):
    """使用crt.sh搜索指定域名的子域名"""
    from app.core.crtsh_monitor import get_crtsh_instance

    crtsh = get_crtsh_instance(args.config)
    crtsh.check_domain(args.search)

def search_all_domains(args):
    """使用crt.sh搜索所有监控域名的子域名"""
    from app.core.crtsh_monitor import get_crtsh_instance

    crtsh = get_crtsh_instance(args.config)
    crtsh.check_all_domains()

def show_recent_changes(args):
    """显示最近的变更记录"""
    config = get_config_instance(args.config)
    manager = get_monitor_manager(args.config)
    color_enabled = config.getboolean('general', 'color_output', fallback=True)

    changes = manager.get_recent_changes(args.limit)

    if not changes:
        print_colored("[!] 没有变更记录", "yellow", color_enabled)
        return

    print_colored(f"\n[*] 最近的变更记录 (共 {len(changes)} 条):", "blue", color_enabled)

    for change in changes:
        domain = change['domain']
        subdomain = change['subdomain']
        action = change['action']
        source = change['source'] or '未知'
        timestamp = change['timestamp']
        notified = "已通知" if change['notified'] else "未通知"

        action_text = "添加" if action == 'add' else "移除"
        print_colored(f"  - {timestamp}: {action_text} {subdomain} (域名: {domain}, 来源: {source}, {notified})", "cyan", color_enabled)

    print()

def start_monitoring(args):
    """开始监控"""
    config = get_config_instance(args.config)
    manager = get_monitor_manager(args.config)
    color_enabled = config.getboolean('general', 'color_output', fallback=True)

    # 设置首次运行通知选项（默认禁用）
    notify_first_run = False
    if hasattr(args, 'notify') and args.notify:
        notify_first_run = True

    config.set('domains', 'notify_on_first_run', str(notify_first_run).lower())
    print_colored(f"[*] 首次运行通知设置为: {notify_first_run}", "blue", color_enabled)

    # 设置动态加载选项（默认启用）
    dynamic_loading = True
    if hasattr(args, 'no_dynamic_load') and args.no_dynamic_load:
        dynamic_loading = False

    config.set('domains', 'dynamic_loading', str(dynamic_loading).lower())
    print_colored(f"[*] 域名文件动态加载设置为: {dynamic_loading}", "blue", color_enabled)

    # 使用-m参数时默认启用强制同步
    force_sync = True  # 默认启用强制同步

    # 检查是否明确禁用强制同步
    if hasattr(args, 'no_force_sync') and args.no_force_sync:
        force_sync = False
        print_colored("[*] 已禁用强制同步", "blue", color_enabled)
    # 检查是否明确启用强制同步（覆盖默认设置）
    elif hasattr(args, 'force_sync') and args.force_sync:
        force_sync = True

    if force_sync:
        print_colored("[*] 启用了强制同步，正在同步域名...", "blue", color_enabled)
        sync_domains(args)
    else:
        # 从文件中添加域名（不删除现有域名）
        print_colored("[*] 从文件中添加域名（不删除现有域名）...", "blue", color_enabled)
        add_domains_from_files(args)

    # 启动所有监控器
    print_colored("[*] 正在启动所有监控器...", "blue", color_enabled)
    manager.start_monitoring()

    print_colored("[*] 监控已启动，按 Ctrl+C 停止", "blue", color_enabled)

    # 如果启用了动态加载，显示提示
    if dynamic_loading:
        print_colored("[*] 域名文件动态加载已启用，可以在运行时修改域名文件", "green", color_enabled)
        print_colored(f"[*] 域名文件检查间隔: {config.getint('domains', 'file_check_interval', fallback=60)}分钟", "green", color_enabled)

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print_colored("\n[*] 正在停止所有监控器...", "blue", color_enabled)
        manager.stop_monitoring()
        print_colored("[*] 监控已停止", "blue", color_enabled)

def sync_domains(args):
    """同步域名数据库与域名文件"""
    config = get_config_instance(args.config)
    manager = get_monitor_manager(args.config)
    color_enabled = config.getboolean('general', 'color_output', fallback=True)

    print_colored("[*] 开始同步域名数据库与域名文件...", "blue", color_enabled)

    # 获取当前所有域名
    current_domains = manager.list_domains()
    current_domain_names = {domain['name'] for domain in current_domains} if current_domains else set()
    current_count = len(current_domain_names)
    print_colored(f"[*] 当前数据库中有 {current_count} 个域名", "blue", color_enabled)

    # 加载域名文件中的域名
    file_domains = set(config.load_domains_from_files())
    file_count = len(file_domains)
    print_colored(f"[*] 域名文件中有 {file_count} 个域名", "blue", color_enabled)

    # 找出需要添加的域名
    domains_to_add = file_domains - current_domain_names
    add_count = len(domains_to_add)

    # 找出需要删除的域名
    domains_to_remove = current_domain_names - file_domains
    remove_count = len(domains_to_remove)

    print_colored(f"[*] 需要添加 {add_count} 个域名，需要删除 {remove_count} 个域名", "blue", color_enabled)

    # 删除不在文件中的域名
    if domains_to_remove:
        print_colored("[*] 正在删除不在文件中的域名...", "yellow", color_enabled)
        for domain in domains_to_remove:
            print_colored(f"  - 删除域名: {domain}", "yellow", color_enabled)
            manager.remove_domain(domain)

    # 添加文件中的新域名
    if domains_to_add:
        print_colored("[*] 正在添加文件中的新域名...", "green", color_enabled)
        for domain in domains_to_add:
            print_colored(f"  - 添加域名: {domain}", "green", color_enabled)
            manager.add_domain(domain, source='file_sync')

    # 获取更新后的域名数量
    updated_domains = manager.list_domains()
    updated_count = len(updated_domains) if updated_domains else 0
    print_colored(f"[*] 同步完成，数据库中现有 {updated_count} 个域名", "blue", color_enabled)

    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description=f"CertMonitor v{VERSION} - 使用certstream和crt.sh监控子域名新增")

    parser.add_argument("-c", "--config", metavar="FILE", help="指定配置文件路径", default="app/config/config.ini")

    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-a", "--add", metavar="DOMAIN", help="添加要监控的域名")
    group.add_argument("-r", "--remove", metavar="DOMAIN", help="从监控列表中移除域名")
    group.add_argument("-l", "--list", action="store_true", help="列出所有监控的域名")
    group.add_argument("-m", "--monitor", action="store_true", help="开始实时监控证书透明度日志")
    group.add_argument("-e", "--export", metavar="DOMAIN", help="导出指定域名的所有子域名")
    group.add_argument("-f", "--file", action="store_true", help="从配置的文件中添加域名")
    group.add_argument("-s", "--search", metavar="DOMAIN", help="使用crt.sh搜索指定域名的子域名")
    group.add_argument("-S", "--search-all", action="store_true", help="使用crt.sh搜索所有监控域名的子域名")
    group.add_argument("-C", "--changes", action="store_true", help="显示最近的变更记录")
    group.add_argument("--sync", action="store_true", help="同步域名数据库与域名文件")

    parser.add_argument("--limit", type=int, default=20, help="显示变更记录的数量限制")
    parser.add_argument("--notify", action="store_true", help="首次运行时发送通知（默认不发送）")
    parser.add_argument("--no-dynamic-load", action="store_true", help="禁用域名文件动态加载（默认启用）")
    parser.add_argument("--force-sync", action="store_true", help="启动监控前强制同步域名（与--monitor一起使用）")
    parser.add_argument("--no-force-sync", action="store_true", help="禁用默认的强制同步（与--monitor一起使用）")

    args = parser.parse_args()

    # 设置日志
    setup_logging(args.config)

    try:
        if args.add:
            add_domain(args)
        elif args.remove:
            remove_domain(args)
        elif args.list:
            list_domains(args)
        elif args.monitor:
            start_monitoring(args)
        elif args.export:
            export_subdomains(args)
        elif args.file:
            add_domains_from_files(args)
        elif args.search:
            search_domain(args)
        elif args.search_all:
            search_all_domains(args)
        elif args.changes:
            show_recent_changes(args)
        elif args.sync:
            sync_domains(args)

    except KeyboardInterrupt:
        print("\n" + colored("[!] 用户中断，退出程序", "yellow"))
    except Exception as e:
        logger = get_logger()
        logger.exception("程序异常")
        print("\n" + colored(f"[!] 发生错误: {str(e)}", "red"))

if __name__ == "__main__":
    main()
