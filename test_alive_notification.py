#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试存活通知功能
"""

import time
from app.core.manager import get_monitor_manager
from app.utils.logger import setup_logging

def test_alive_notification():
    """测试存活通知功能"""
    print("🧪 测试存活通知功能")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 创建管理器实例
    manager = get_monitor_manager()
    
    # 临时设置较短的通知间隔（30秒）用于测试
    manager.alive_notification_interval = 30
    
    print(f"📊 存活通知间隔: {manager.alive_notification_interval}秒")
    print(f"📊 通知器状态: {'启用' if manager.notifier.enabled else '禁用'}")
    
    # 启动存活通知
    manager._start_alive_notification()
    
    print("⏳ 等待存活通知发送...")
    print("   (预计30秒后发送第一条通知)")
    
    try:
        # 等待40秒，确保通知发送
        for i in range(40):
            print(f"\r⏱️  等待中... {40-i}秒", end="", flush=True)
            time.sleep(1)
        
        print("\n✅ 测试完成")
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被中断")
    
    finally:
        # 停止存活通知
        manager._stop_alive_notification()
        print("🛑 存活通知已停止")

if __name__ == "__main__":
    test_alive_notification()
