# CertMonitor 存活监控

## 🎯 简介

CertMonitor 内置了简单的心跳监控功能，可以帮助您检查脚本是否正常运行。

## 📊 监控功能

### 1. **内置心跳机制**

当 CertMonitor 运行时，会自动：
- 每60秒更新一次心跳文件 `status/heartbeat.json`
- 记录进程状态、监控域名数量、运行时间等信息

### 2. **状态检查命令**

```bash
# 检查监控状态
python3 certmonitor.py --status
```

输出示例：
```
📊 CertMonitor 状态检查
==================================================
✅ 心跳状态: 正常 (45秒前)
   进程PID: 12345
   监控域名: 5个
   运行时间: 3600秒

✅ 日志活动: 正常 (10秒前更新)
   文件大小: 1024000 字节
   最后修改: 2025-05-29 21:15:29

✅ 进程状态: 运行中 (1个进程)
   PID: 12345
==================================================

💡 使用提示:
   启动监控: python3 certmonitor.py -m
   查看日志: tail -f logs/certmonitor_$(date +%Y-%m-%d).log
   停止监控: 在运行的终端按 Ctrl+C
```

## 🔍 监控指标

### 心跳状态
- ✅ **正常**: 2分钟内有心跳更新
- ⚠️ **超时**: 超过2分钟没有心跳更新
- ❌ **异常**: 心跳文件不存在或读取失败

### 日志活动
- ✅ **正常**: 10分钟内有日志更新
- ⚠️ **无活动**: 超过10分钟没有日志更新
- ❌ **异常**: 日志文件不存在

### 进程状态
- ✅ **运行中**: 找到 CertMonitor 进程
- ❌ **未运行**: 没有找到相关进程
- ⚠️ **无法检查**: 需要安装 psutil 库

## 🛠️ 使用方法

### 1. **启动监控**
```bash
# 添加域名
python3 certmonitor.py -a example.com

# 启动监控（自动启用心跳）
python3 certmonitor.py -m
```

### 2. **检查状态**
```bash
# 检查当前状态
python3 certmonitor.py --status
```

### 3. **查看实时日志**
```bash
# 查看今天的日志
tail -f logs/certmonitor_$(date +%Y-%m-%d).log

# 查看心跳文件
cat status/heartbeat.json
```

### 4. **停止监控**
在运行监控的终端按 `Ctrl+C`

## 📁 文件说明

### 心跳文件 (`status/heartbeat.json`)
```json
{
  "timestamp": "2025-05-29T21:15:30.123456",
  "status": "running",
  "pid": 12345,
  "monitored_domains": 5,
  "monitors": {
    "crtsh": {"enabled": true, "running": true},
    "ctlog": {"enabled": true, "running": true, "logs_count": 3},
    "notifier": {"enabled": true, "running": true}
  },
  "stats": {
    "uptime_seconds": 3600,
    "last_activity": "2025-05-29T21:15:30.123456"
  }
}
```

### 日志文件
- `logs/certmonitor_YYYY-MM-DD.log` - 按日期分割的日志文件
- 自动压缩旧日志文件
- 保留30天的日志记录

## 🚨 故障排除

### 心跳超时
```bash
# 检查进程是否还在运行
ps aux | grep certmonitor

# 查看最新日志
tail -20 logs/certmonitor_$(date +%Y-%m-%d).log

# 重新启动监控
python3 certmonitor.py -m
```

### 进程未运行
```bash
# 检查是否有错误
python3 certmonitor.py --status

# 查看启动日志
tail -50 logs/certmonitor_$(date +%Y-%m-%d).log

# 重新启动
python3 certmonitor.py -m
```

### 日志无活动
可能原因：
- 没有新的证书数据
- 网络连接问题
- 监控域名配置问题

## 📋 定时检查脚本

可以创建一个简单的定时检查脚本：

```bash
#!/bin/bash
# check_certmonitor.sh

# 检查状态并记录到日志
python3 /path/to/certmonitor/certmonitor.py --status >> /var/log/certmonitor_check.log 2>&1

# 如果心跳超时，发送告警（可选）
if python3 /path/to/certmonitor/certmonitor.py --status | grep -q "心跳状态: 超时"; then
    echo "CertMonitor 心跳超时" | mail -s "CertMonitor Alert" <EMAIL>
fi
```

添加到 crontab：
```bash
# 每5分钟检查一次
*/5 * * * * /path/to/check_certmonitor.sh
```

## 💡 最佳实践

1. **定期检查状态** - 建议每5-10分钟检查一次
2. **监控日志大小** - 防止日志文件过大
3. **备份重要数据** - 定期备份数据库和配置文件
4. **网络监控** - 确保网络连接正常
5. **资源监控** - 监控CPU和内存使用情况

## 🔧 扩展监控

如果需要更高级的监控功能，可以：

1. **集成到监控系统** - 将状态检查集成到 Prometheus、Zabbix 等
2. **告警通知** - 配置邮件、微信、钉钉等告警
3. **自动重启** - 编写脚本在异常时自动重启
4. **性能监控** - 监控CPU、内存、网络使用情况

通过这个简单的内置监控功能，您可以轻松了解 CertMonitor 的运行状态！
