#!/bin/bash

# CertMonitor 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    log_info "Python版本: $(python3 --version)"
}

# 检查依赖
check_dependencies() {
    log_info "检查Python依赖..."
    
    if ! python3 -c "import loguru" &> /dev/null; then
        log_error "loguru 未安装，请运行: pip install -r requirements.txt"
        exit 1
    fi
    
    if ! python3 -c "import psutil" &> /dev/null; then
        log_warn "psutil 未安装，状态检查功能可能不可用"
        log_info "安装命令: pip install psutil"
    fi
    
    log_info "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p status
    mkdir -p project
    
    log_info "目录创建完成"
}

# 检查配置文件
check_config() {
    if [ ! -f "app/config/config.ini" ]; then
        log_error "配置文件不存在: app/config/config.ini"
        exit 1
    fi
    
    log_info "配置文件检查完成"
}

# 启动CertMonitor
start_certmonitor() {
    log_info "启动 CertMonitor..."
    
    # 检查是否已有进程在运行
    if pgrep -f "certmonitor.py" > /dev/null; then
        log_warn "CertMonitor 进程已在运行"
        log_info "当前运行的进程:"
        pgrep -f "certmonitor.py" | while read pid; do
            echo "  PID: $pid"
        done
        
        read -p "是否要停止现有进程并重新启动? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "停止现有进程..."
            pkill -f "certmonitor.py" || true
            sleep 3
        else
            log_info "保持现有进程运行"
            return 0
        fi
    fi
    
    # 启动监控
    log_info "正在启动监控进程..."
    nohup python3 certmonitor.py -m > logs/startup.log 2>&1 &
    
    # 等待启动
    sleep 5
    
    # 检查是否启动成功
    if pgrep -f "certmonitor.py" > /dev/null; then
        log_info "CertMonitor 启动成功!"
        log_info "PID: $(pgrep -f 'certmonitor.py')"
    else
        log_error "CertMonitor 启动失败"
        log_error "请检查 logs/startup.log 获取详细信息"
        exit 1
    fi
}

# 启动守护进程监控
start_daemon() {
    log_info "启动守护进程监控..."
    
    # 检查是否已有守护进程在运行
    if [ -f "status/daemon.pid" ]; then
        daemon_pid=$(cat status/daemon.pid)
        if kill -0 "$daemon_pid" 2>/dev/null; then
            log_warn "守护进程已在运行 (PID: $daemon_pid)"
            return 0
        else
            log_info "删除过期的PID文件"
            rm -f status/daemon.pid
        fi
    fi
    
    # 启动守护进程
    nohup python3 monitor_daemon.py -d > logs/daemon.log 2>&1 &
    
    sleep 2
    
    if [ -f "status/daemon.pid" ]; then
        daemon_pid=$(cat status/daemon.pid)
        log_info "守护进程启动成功! (PID: $daemon_pid)"
    else
        log_warn "守护进程启动可能失败，请检查 logs/daemon.log"
    fi
}

# 显示状态
show_status() {
    log_info "检查运行状态..."
    
    if command -v python3 &> /dev/null && python3 -c "import psutil" &> /dev/null; then
        python3 check_status.py
    else
        log_warn "psutil 未安装，使用简单状态检查"
        
        if pgrep -f "certmonitor.py" > /dev/null; then
            log_info "✅ CertMonitor 进程正在运行"
            pgrep -f "certmonitor.py" | while read pid; do
                echo "  PID: $pid"
            done
        else
            log_error "❌ CertMonitor 进程未运行"
        fi
        
        if [ -f "status/daemon.pid" ]; then
            daemon_pid=$(cat status/daemon.pid)
            if kill -0 "$daemon_pid" 2>/dev/null; then
                log_info "✅ 守护进程正在运行 (PID: $daemon_pid)"
            else
                log_error "❌ 守护进程未运行"
            fi
        else
            log_error "❌ 守护进程未运行"
        fi
    fi
}

# 主函数
main() {
    echo "🚀 CertMonitor 启动脚本"
    echo "========================"
    
    # 基础检查
    check_python
    check_dependencies
    create_directories
    check_config
    
    echo
    echo "选择操作:"
    echo "1) 启动 CertMonitor"
    echo "2) 启动 CertMonitor + 守护进程监控"
    echo "3) 仅启动守护进程监控"
    echo "4) 检查状态"
    echo "5) 退出"
    
    read -p "请选择 (1-5): " -n 1 -r
    echo
    
    case $REPLY in
        1)
            start_certmonitor
            ;;
        2)
            start_certmonitor
            start_daemon
            ;;
        3)
            start_daemon
            ;;
        4)
            show_status
            ;;
        5)
            log_info "退出"
            exit 0
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
    
    echo
    log_info "启动完成!"
    log_info "使用以下命令检查状态:"
    log_info "  python3 check_status.py          # 单次检查"
    log_info "  python3 check_status.py -w       # 持续监控"
    log_info "  tail -f logs/certmonitor_$(date +%Y-%m-%d).log  # 查看日志"
}

# 执行主函数
main "$@"
