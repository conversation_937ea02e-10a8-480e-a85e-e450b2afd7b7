# CertMonitor

CertMonitor是一个直接监控Certificate Transparency日志以检测新增子域名的工具。它通过直接访问CT日志API来发现和跟踪指定域名的新子域名。

## 功能特点
- 直接监控Let's Encrypt和Cloudflare CT日志，获取实时证书数据
- 使用crt.sh定期查询历史证书记录
- 即时检测并报告新增子域名
- 支持域名文件动态加载，可在运行时修改监控列表
- 验证子域名有效性，只存储和通知有效域名
- 获取子域名网站标题和响应大小等信息
- 根据子域名首次发现日期筛选，只关注最近的新域名
- 支持微信通知，及时获取新发现的子域名
- 高效的SQL连接池，提高数据库操作性能
- 彩色输出，易于识别新发现的子域名
- 简单易用的命令行界面

## 安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/certmonitor.git
cd certmonitor

# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或者 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

## 使用方法

```bash
# 添加单个域名到监控列表
python certmonitor.py -a example.com

# 列出所有监控的域名
python certmonitor.py -l

# 开始监控（默认不发送首次运行通知，启用域名文件动态加载）
python certmonitor.py -m

# 开始监控并发送首次运行通知
python certmonitor.py -m --notify

# 开始监控但禁用域名文件动态加载
python certmonitor.py -m --no-dynamic-load

# 从监控列表中移除域名
python certmonitor.py -r example.com

# 导出指定域名的所有子域名
python certmonitor.py -e example.com

# 使用crt.sh搜索指定域名的子域名
python certmonitor.py -s example.com

# 显示最近的变更记录
python certmonitor.py -C

# 显示帮助信息
python certmonitor.py -h
```

更多详细用法请参考 `CertMonitor使用说明.md`。

## 工作原理

CertMonitor 使用两种方式来发现子域名：

1. **实时监控（CertStream）**：
   - 使用 certstream 库连接到证书透明度日志的实时流
   - 当新的 SSL/TLS 证书被颁发并记录到证书透明度日志时，certstream 会接收到这些信息
   - CertMonitor 会分析这些证书中包含的域名，检查是否有与监控列表中的域名相关的新子域名

2. **定期查询（crt.sh）**：
   - 定期查询 crt.sh 网站，获取历史证书记录
   - 提取证书中的子域名和首次发现日期
   - 根据首次发现日期筛选，只关注最近的新域名

当发现新的子域名时，CertMonitor 会：
1. 验证域名是否有效（可解析）
2. 获取网站标题、响应大小和状态码等信息
3. 将有效域名保存到数据库中
4. 发送通知（如果启用）

为了提高性能和可靠性，CertMonitor 使用了以下技术：
1. **SQL 连接池**：高效管理数据库连接，提高性能
2. **域名验证**：只存储和通知有效域名，减少噪音
3. **日期筛选**：根据子域名首次发现日期筛选，只关注最近的新域名
4. **动态加载**：支持在运行时修改监控列表，无需重启程序
5. **健壮的连接管理**：自动重连机制，处理连接中断情况

## 项目结构

```
.
├── app/                    # 应用程序目录
│   ├── config/             # 配置文件目录
│   │   ├── config.ini      # 主配置文件
│   │   └── custom_domains.txt  # 自定义域名文件
│   ├── core/               # 核心功能目录
│   │   ├── certstream_monitor.py  # CertStream 监控模块
│   │   ├── crtsh_monitor.py  # crt.sh 监控模块
│   │   ├── manager.py      # 监控管理器
│   │   ├── robust_certstream.py  # 健壮的 CertStream 客户端
│   │   └── subdomain_processor.py  # 子域名处理器
│   ├── db/                 # 数据库相关目录
│   │   ├── manager.py      # 数据库管理器
│   │   └── pool.py         # 数据库连接池
│   ├── notifiers/          # 通知相关目录
│   │   └── notifier.py     # 通知管理器
│   └── utils/              # 工具类目录
│       ├── config.py       # 配置管理器
│       ├── domain_validator.py  # 域名验证器
│       ├── file_watcher.py  # 文件监控器
│       └── logger.py       # 日志管理器
├── domain/                 # 域名文件目录
├── logs/                   # 日志文件目录
│   └── certmonitor.log     # 主日志文件
├── project/                # 项目域名文件目录
├── certmonitor.py          # 主程序入口
├── project_domains_manager.py  # 项目域名管理工具
├── requirements.txt        # 依赖列表
├── CertMonitor使用说明.md   # 使用说明文档
└── README.md               # 项目说明文档
```

## 主要组件说明

1. **核心组件**：
   - `certstream_monitor.py`: 使用 CertStream 实时监控证书透明度日志
   - `crtsh_monitor.py`: 定期查询 crt.sh 网站获取历史证书记录
   - `manager.py`: 管理所有监控器，协调它们的工作
   - `subdomain_processor.py`: 处理子域名的验证、存储和通知

2. **数据库组件**：
   - `pool.py`: 实现 SQL 连接池，高效管理数据库连接
   - `manager.py`: 提供数据库操作接口，如添加域名、查询子域名等

3. **通知组件**：
   - `notifier.py`: 实现通知功能，支持微信通知

4. **工具组件**：
   - `config.py`: 管理配置文件
   - `domain_validator.py`: 验证域名有效性，获取网站信息
   - `file_watcher.py`: 监控域名文件变化
   - `logger.py`: 管理日志记录

## 依赖

- Python 3.6+
- certstream: 连接证书透明度日志
- requests: HTTP 请求库
- beautifulsoup4: HTML 解析库
- pymysql: MySQL 数据库驱动
- termcolor: 终端彩色输出
- argparse: 命令行参数解析

## 许可证

MIT

