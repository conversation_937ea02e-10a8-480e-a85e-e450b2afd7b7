# CertMonitor 使用说明

CertMonitor 是一个强大的子域名监控工具，通过监控证书透明度日志和查询证书数据库，实时发现和跟踪子域名变化。本文档详细介绍了 CertMonitor 的各项功能和使用方法。

## 1. 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 基本使用

```bash
# 启动监控（默认启用强制同步，不发送首次运行通知，启用域名文件动态加载）
python certmonitor.py -m

# 添加域名到监控列表
python certmonitor.py -a example.com

# 列出所有监控的域名
python certmonitor.py -l

# 导出子域名
python certmonitor.py -e example.com

# 查看最近的变更记录
python certmonitor.py -C

# 同步域名数据库与域名文件
python certmonitor.py --sync

# 启动监控前强制同步域名（现在是默认行为）
python certmonitor.py -m

# 禁用强制同步（如果不想同步域名）
python certmonitor.py -m --no-force-sync
```

## 2. 命令行参数

```
usage: certmonitor.py [-h] [-c CONFIG] [-m] [-a DOMAIN] [-r DOMAIN] [-l] [-e EXPORT] [-s DOMAIN] [-S] [-C] [--sync] [--limit LIMIT] [--notify] [--no-dynamic-load] [--force-sync] [--no-force-sync]

CertMonitor - 证书透明度日志监控工具

optional arguments:
  -h, --help            显示帮助信息并退出
  -c CONFIG, --config CONFIG
                        配置文件路径
  -m, --monitor         启动监控
  -a DOMAIN, --add DOMAIN
                        添加域名到监控列表
  -r DOMAIN, --remove DOMAIN
                        从监控列表中移除域名
  -l, --list            列出所有监控的域名
  -e EXPORT, --export EXPORT
                        导出指定域名的子域名
  -s DOMAIN, --search DOMAIN
                        使用crt.sh搜索指定域名的子域名
  -S, --search-all      使用crt.sh搜索所有监控域名的子域名
  -C, --changes         显示最近的变更记录
  --sync                同步域名数据库与域名文件
  --limit LIMIT         显示变更记录的数量限制
  --notify              首次运行时发送通知（默认不发送）
  --no-dynamic-load     禁用域名文件动态加载（默认启用）
  --force-sync          启动监控前强制同步域名（与--monitor一起使用）
  --no-force-sync       禁用默认的强制同步（与--monitor一起使用）
```

### 参数说明

- **-m, --monitor**: 启动监控。默认情况下，启用强制同步，首次运行不发送通知，启用域名文件动态加载。
- **-a, --add DOMAIN**: 添加域名到监控列表。
- **-r, --remove DOMAIN**: 从监控列表中移除域名。
- **-l, --list**: 列出所有监控的域名。
- **-e, --export EXPORT**: 导出指定域名的子域名。
- **-s, --search DOMAIN**: 使用crt.sh搜索指定域名的子域名。
- **-S, --search-all**: 使用crt.sh搜索所有监控域名的子域名。
- **-C, --changes**: 显示最近的变更记录。
- **--sync**: 同步域名数据库与域名文件，确保数据库中的域名与域名文件中的域名完全一致。
- **--limit LIMIT**: 显示变更记录的数量限制，默认为20。
- **--notify**: 首次运行时发送通知（默认不发送）。
- **--no-dynamic-load**: 禁用域名文件动态加载（默认启用）。
- **--force-sync**: 启动监控前强制同步域名，与 `--monitor` 一起使用（现在是默认行为）。
- **--no-force-sync**: 禁用默认的强制同步，与 `--monitor` 一起使用。

## 3. 配置文件

配置文件位于 `app/config/config.ini`，包含以下主要配置项：

```ini
[general]
# 日志级别
log_level = INFO
# 日志文件路径
log_file = logs/certmonitor.log
# 是否启用彩色输出
color_output = true

[domains]
# 域名文件路径，多个路径用逗号分隔
domain_files = domains.txt, project
# 是否递归搜索子目录中的域名文件
recursive_search = false
# 域名文件的扩展名，多个扩展名用逗号分隔
domain_file_extensions = .txt, .list, .domains
# 新增域名时是否立即进行子域名收集
scan_new_domains = true
# 首次运行时是否发送通知
notify_on_first_run = false
# 是否启用域名文件动态加载
dynamic_loading = true
# 域名文件检查间隔（分钟）
file_check_interval = 60
# 是否自动移除不在域名文件中的域名
auto_remove_domains = true
# 是否加载自定义域名文件
load_custom_domains = false

[database]
# 数据库类型
type = mysql
# 数据库主机
host = localhost
# 数据库端口
port = 3306
# 数据库用户名
username = root
# 数据库密码
password = password
# 数据库名称
database = certmonitor
# 连接池大小
pool_size = 5
# 连接超时时间（秒）
timeout = 10

[certstream]
# 是否启用certstream监控
enabled = true
# certstream服务器URL
url = wss://certstream.calidog.io/
# 备用certstream服务器URL，多个URL用逗号分隔
backup_urls = wss://certstream.digicert.com/
# 是否跳过心跳消息
skip_heartbeats = true
# 重连间隔（秒）
reconnect_interval = 5

[crtsh]
# 是否启用crt.sh搜索
enabled = true
# 搜索间隔（小时）
interval = 24
# 每次查询的超时时间（秒）
timeout = 60
# 每次查询的重试次数
retries = 5
# 每次查询的延迟时间（秒）
delay = 30
# 是否使用缓存
use_cache = true
# 缓存有效期（秒）
cache_ttl = 3600

[export]
# 导出目录
export_dir = exports

[notification]
# 是否启用通知
enabled = true
# 检查未通知变更的间隔时间（秒）
check_interval = 60
# 批量通知的大小
batch_size = 10
# 批量通知的超时时间（秒）
batch_timeout = 300

[wechat]
# 是否启用微信通知
enabled = false
# 企业微信机器人webhook地址
webhook_url =
# 请求超时时间（秒）
timeout = 10
# 最大重试次数
retries = 3
# 重试延迟时间（秒）
delay = 2
# 速率限制（每分钟最大消息数）
rate_limit = 20
```

## 4. 域名文件动态加载

CertMonitor 支持动态加载域名文件，这意味着您可以在程序运行时添加、修改或删除域名文件，系统会自动检测变化并更新监控的域名列表。

### 域名文件格式

域名文件应该是纯文本文件，每行一个域名。以 `#` 开头的行将被视为注释，会被忽略。例如：

```
# 这是一个域名文件
# 每行一个域名，以#开头的行将被忽略

example.com
example.org
```

### 监控 project 目录下的域名文件

CertMonitor 默认会监控 `project` 目录下的所有 `.txt` 文件，并将其中的域名添加到监控列表中。您可以在程序运行时添加、修改或删除这些文件，系统会自动更新监控的域名列表。

```bash
# 启动监控
python certmonitor.py -m

# 在另一个终端中创建新的域名文件
echo "example.net" > project/new_domain.txt
```

系统会自动检测到新的域名文件，并将其中的域名添加到监控列表中。

### 域名文件动态加载和自动删除

CertMonitor 不仅支持动态添加域名，还支持动态删除域名。当您从域名文件中删除域名或删除整个域名文件时，系统会自动从监控列表中移除相应的域名。

#### 域名文件检查间隔

系统会定期检查域名文件是否有变化，默认检查间隔为 60 分钟。可以在配置文件中修改：

```ini
[domains]
# 域名文件检查间隔（分钟）
file_check_interval = 60
```

这个值表示检查域名文件变化的间隔时间，单位是分钟。如果您希望更频繁地检查域名文件变化，可以减小这个值；如果您希望减少系统资源消耗，可以增大这个值。

```bash
# 启动监控
python certmonitor.py -m

# 在另一个终端中修改域名文件，删除一个域名
sed -i '/example.org/d' project/domains.txt

# 或者删除整个域名文件
rm project/some_domain_file.txt
```

系统会自动检测到域名文件的变化，并从监控列表中移除相应的域名。

#### 配置自动删除功能

默认情况下，自动删除功能是启用的。如果您希望禁用此功能，可以在配置文件中修改：

```ini
[domains]
auto_remove_domains = false
```

禁用后，即使域名文件中的域名被删除，系统也不会自动从监控列表中移除这些域名。

#### 域名文件变化处理

系统会检测以下几种域名文件变化：

1. **新增域名文件**：系统会自动加载新增的域名文件中的域名。
2. **修改域名文件**：系统会自动更新修改的域名文件中的域名，添加新域名，删除已移除的域名。
3. **删除域名文件**：系统会自动从监控列表中移除已删除文件中的域名。
4. **域名文件内容变化**：系统会自动添加新增的域名，删除已移除的域名。

#### 自定义域名文件

系统默认只监控 `project` 目录下的域名文件，不会加载 `app/config/custom_domains.txt` 文件。如果您希望同时加载自定义域名文件，可以在配置文件中修改：

```ini
[domains]
load_custom_domains = true
```

启用后，系统会同时监控 `project` 目录下的域名文件和 `app/config/custom_domains.txt` 文件。

#### 域名文件优先级

当同一个域名出现在多个域名文件中时，系统会将其视为一个域名，只添加一次。当所有包含该域名的文件都被删除或修改（删除该域名）时，系统才会从监控列表中移除该域名。

例如，如果 `example.com` 同时出现在 `project/domains1.txt` 和 `project/domains2.txt` 中，只有当这两个文件都被删除或修改（删除 `example.com`）时，系统才会从监控列表中移除 `example.com`。

## 5. 首次运行通知控制

默认情况下，CertMonitor 在首次运行时不会发送通知，这样可以避免在初始导入大量域名时产生大量通知。如果您希望在首次运行时发送通知，可以使用 `--notify` 参数：

```bash
# 启动监控，首次运行时发送通知
python certmonitor.py -m --notify
```

## 6. 使用示例

### 基本监控

```bash
# 启动监控
python certmonitor.py -m
```

### 添加域名

```bash
# 添加单个域名
python certmonitor.py -a example.com

# 从文件中添加域名
echo "example.org" > domains.txt
python certmonitor.py -m
```

### 查看监控的域名

```bash
# 列出所有监控的域名
python certmonitor.py -l
```

### 导出子域名

```bash
# 导出指定域名的子域名
python certmonitor.py -e example.com
```

### 查看变更记录

```bash
# 显示最近的变更记录
python certmonitor.py -C

# 显示更多变更记录
python certmonitor.py -C --limit 50
```

### 搜索子域名

```bash
# 使用crt.sh搜索指定域名的子域名
python certmonitor.py -s example.com

# 使用crt.sh搜索所有监控域名的子域名
python certmonitor.py -S
```

### 同步域名

同步功能可以确保数据库中的域名与域名文件中的域名完全一致。这个功能会删除数据库中存在但域名文件中不存在的域名，并添加域名文件中存在但数据库中不存在的域名。

```bash
# 同步域名数据库与域名文件
python certmonitor.py --sync

# 启动监控前强制同步域名
python certmonitor.py -m --force-sync
```

这个功能特别适用于以下情况：
- 域名文件发生了大量变化
- 数据库中的域名与域名文件不一致
- 您希望确保只监控域名文件中的域名
- 您遇到了域名文件变化后，数据库中的域名没有正确更新的问题

同步过程会显示详细的信息，包括：
- 当前数据库中的域名数量
- 域名文件中的域名数量
- 需要添加的域名数量
- 需要删除的域名数量
- 同步完成后数据库中的域名数量

## 7. 高级功能

### 7.1 域名验证和信息获取

CertMonitor 会在发现新子域名后，进行域名验证和信息获取，只对有效的域名发送通知。这个功能可以帮助您过滤掉无效的域名，减少不必要的通知，同时提供更丰富的域名信息。

#### 工作原理

1. **域名验证**：系统使用 DNS 解析来验证域名是否有效。如果域名无法解析，则被视为无效域名，不会发送通知。
2. **信息获取**：对于有效的域名，系统会尝试通过 HTTP 和 HTTPS 访问网站，获取标题、响应大小和状态码等信息。
3. **通知控制**：系统只对有效的域名发送通知，对于无效的域名，系统会记录但不发送通知。
4. **通知内容增强**：通知内容中会包含网站标题、响应大小和状态码等信息，提供更丰富的上下文。

#### 通知内容示例

```
### 发现新子域名

**域名**: example.com

**子域名**: new.example.com

**来源**: certstream

**标题**: Example Domain

**响应大小**: 1.25 KB

**状态码**: 200

**URL**: https://new.example.com

**发现时间**: 2023-05-15 10:30:45
```

### 7.2 子域名日期筛选

CertMonitor 支持根据子域名的首次发现日期进行筛选，只对最近新出现的子域名进行 DNS 解析、获取标题和返回大小，然后发送通知。这个功能可以帮助您专注于真正的新子域名，而不是那些已经存在很长时间的子域名，同时减少不必要的网络请求和通知。

#### 工作原理

1. **日期提取**：系统从 crt.sh 返回的证书数据中提取子域名的首次发现日期（证书的 `not_before` 字段）。
2. **日期筛选**：系统会将提取的日期与当前日期进行比较，只有在配置的天数阈值内首次发现的子域名才会被视为"最近的"子域名。
3. **资源优化**：系统只对"最近的"子域名进行 DNS 解析、获取标题和返回大小，对于非最近的子域名，系统会跳过这些操作，直接添加到数据库中。
4. **通知控制**：系统只对"最近的"子域名发送通知，对于那些首次发现日期较早的子域名，系统会记录但不发送通知。

#### 配置选项

在 `app/config/config.ini` 文件中，您可以配置以下选项：

```ini
[crtsh]
# 最近子域名的天数阈值（只对最近N天内首次发现的子域名进行验证和发送通知）
recent_days_threshold = 30
```

#### 优势

1. **减少网络请求**：只对最近的子域名进行 DNS 解析和 HTTP 请求，减少网络流量和服务器负载。
2. **提高效率**：跳过对非最近子域名的验证和信息获取，提高处理速度。
3. **减少通知干扰**：只对最近的子域名发送通知，减少不必要的通知干扰。
4. **保留完整记录**：所有子域名（无论是否最近）都会被添加到数据库中，保留完整的历史记录。

### 7.3 有效域名存储

CertMonitor 只会将有效的域名（可解析且可访问的域名）存储到数据库中，并且确保不会存储重复的域名。这个功能可以帮助您保持数据库的干净和高效，只关注真正有价值的子域名。

#### 工作原理

1. **域名验证**：系统会尝试解析域名，验证其是否有效（可解析）。
2. **有效性筛选**：只有验证为有效的域名才会被添加到数据库中。
3. **重复性检查**：数据库表结构中的唯一性约束确保不会存储重复的域名。
4. **通知控制**：系统只对有效的域名发送通知，对于无效的域名，系统会记录但不存储也不发送通知。

## 8. 故障排除

### 8.1 数据库连接问题

如果遇到数据库连接问题，请检查配置文件中的数据库配置是否正确，并确保数据库服务器正在运行。

### 8.2 CertStream 连接不稳定

CertStream 连接可能不稳定，系统会自动尝试重新连接。如果连接问题持续存在，可以尝试修改配置文件中的 `certstream.url` 和 `certstream.backup_urls` 选项。

### 8.3 crt.sh 查询限制

crt.sh 网站可能会限制查询频率，系统会自动添加延迟和重试机制。如果查询问题持续存在，可以尝试修改配置文件中的 `crtsh.delay` 和 `crtsh.retries` 选项。

### 8.4 日志文件

如果遇到问题，请查看日志文件 `logs/certmonitor.log`，其中包含详细的错误信息和调试信息。
