# CertMonitor 存活监控指南

## 🎯 概述

CertMonitor 提供了多种方式来监控脚本的存活状态，确保持续监控功能正常运行。

## 📊 监控机制

### 1. **心跳文件机制**

CertMonitor 会每30秒更新一次心跳文件 `status/heartbeat.json`，包含以下信息：

```json
{
  "timestamp": "2025-05-29T10:30:15.123456",
  "status": "running",
  "pid": 12345,
  "monitored_domains": 5,
  "monitors": {
    "crtsh": {
      "enabled": true,
      "running": true
    },
    "ctlog": {
      "enabled": true,
      "running": true,
      "logs_count": 3
    },
    "notifier": {
      "enabled": true,
      "running": true
    }
  },
  "stats": {
    "uptime_seconds": 3600,
    "last_activity": "2025-05-29T10:30:15.123456"
  }
}
```

### 2. **进程监控**

通过检查进程状态确认 CertMonitor 是否正在运行。

### 3. **日志活动监控**

检查日志文件的最后修改时间，确认程序是否有活动。

## 🛠️ 监控工具

### 1. **状态检查工具** (`check_status.py`)

#### 基本用法

```bash
# 单次状态检查
python3 check_status.py

# 持续监控模式
python3 check_status.py -w

# 自定义监控间隔（60秒）
python3 check_status.py -w -i 60

# JSON格式输出
python3 check_status.py --json
```

#### 输出示例

```
📊 CertMonitor 状态检查
==================================================
✅ 心跳状态: 心跳正常 (15秒前)
   status: running
   pid: 12345
   monitored_domains: 5

✅ 进程状态: 进程 12345 正在运行
   name: python3
   status: running
   cpu_percent: 2.5

✅ 日志活动: 日志文件活跃 (5秒前更新)
   size: 1024000
   last_modified: 2025-05-29 10:30:15

==================================================
🎉 CertMonitor 运行正常
```

### 2. **守护进程监控器** (`monitor_daemon.py`)

自动监控 CertMonitor 进程，在异常时自动重启。

#### 基本用法

```bash
# 前台运行守护进程
python3 monitor_daemon.py

# 后台运行守护进程
python3 monitor_daemon.py -d

# 自定义检查间隔和重启阈值
python3 monitor_daemon.py -i 60 -t 300
```

#### 参数说明

- `-i, --interval`: 检查间隔（秒），默认60秒
- `-t, --threshold`: 重启阈值（秒），默认300秒
- `-d, --daemon`: 以守护进程模式运行

### 3. **启动脚本** (`start_monitor.sh`)

一键启动和管理 CertMonitor。

```bash
# 运行启动脚本
./start_monitor.sh
```

提供以下选项：
1. 启动 CertMonitor
2. 启动 CertMonitor + 守护进程监控
3. 仅启动守护进程监控
4. 检查状态
5. 退出

## 🔧 部署建议

### 1. **生产环境部署**

```bash
# 1. 启动 CertMonitor 和守护进程
./start_monitor.sh
# 选择选项 2

# 2. 设置定时检查（可选）
# 添加到 crontab
*/5 * * * * cd /path/to/certmonitor && python3 check_status.py --json >> logs/health_check.log 2>&1
```

### 2. **系统服务部署**

创建 systemd 服务文件 `/etc/systemd/system/certmonitor.service`：

```ini
[Unit]
Description=CertMonitor Service
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/certmonitor
ExecStart=/usr/bin/python3 certmonitor.py -m
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：

```bash
sudo systemctl enable certmonitor
sudo systemctl start certmonitor
sudo systemctl status certmonitor
```

## 📱 监控告警

### 1. **脚本监控**

创建监控脚本 `health_monitor.sh`：

```bash
#!/bin/bash

# 检查 CertMonitor 状态
status=$(python3 check_status.py --json | jq -r '.heartbeat.status')

if [ "$status" != "ok" ]; then
    # 发送告警（邮件、微信、钉钉等）
    echo "CertMonitor 异常: $status" | mail -s "CertMonitor Alert" <EMAIL>
fi
```

### 2. **Prometheus 监控**

可以扩展 `check_status.py` 输出 Prometheus 格式的指标：

```python
# 添加到 check_status.py
def export_prometheus_metrics():
    """导出 Prometheus 格式的指标"""
    results = check_all_status()
    
    metrics = []
    metrics.append(f'certmonitor_up{{}} {1 if results["heartbeat"]["status"] == "ok" else 0}')
    metrics.append(f'certmonitor_heartbeat_age_seconds{{}} {get_heartbeat_age()}')
    
    return '\n'.join(metrics)
```

## 🚨 故障排除

### 1. **心跳文件不存在**

- 检查 CertMonitor 是否正在运行
- 检查 `status/` 目录权限
- 查看日志文件获取错误信息

### 2. **心跳超时**

- CertMonitor 进程可能卡死
- 检查系统资源（CPU、内存、磁盘）
- 查看日志文件获取错误信息

### 3. **进程不存在**

- CertMonitor 进程已退出
- 检查日志文件获取退出原因
- 使用守护进程自动重启

### 4. **日志文件无活动**

- 可能没有新的证书数据
- 检查网络连接
- 检查监控域名配置

## 📋 最佳实践

1. **使用守护进程监控** - 确保自动重启异常进程
2. **定期检查状态** - 设置定时任务检查健康状态
3. **监控日志大小** - 防止日志文件过大
4. **备份配置文件** - 定期备份重要配置
5. **设置告警机制** - 及时发现和处理异常

## 📞 监控命令速查

```bash
# 快速状态检查
python3 check_status.py

# 持续监控
python3 check_status.py -w

# 查看实时日志
tail -f logs/certmonitor_$(date +%Y-%m-%d).log

# 检查进程
ps aux | grep certmonitor

# 检查心跳文件
cat status/heartbeat.json | jq

# 启动守护进程
python3 monitor_daemon.py -d

# 停止所有相关进程
pkill -f certmonitor
```

通过这些监控机制，您可以确保 CertMonitor 持续稳定运行，及时发现和处理异常情况。
